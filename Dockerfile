### Builder
FROM python:3.10.15-alpine3.20 AS builder

RUN apk add --no-cache  poetry gcc python3-dev musl-dev linux-headers git
WORKDIR /src
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache
RUN touch README.md
COPY pyproject.toml poetry.lock /src/
RUN poetry env use python3.10
RUN poetry install --without dev

### Image
FROM python:3.10.15-alpine3.20

# Packages
RUN apk add --no-cache su-exec nginx

# Environment variables
ENV PATH="/src/.venv/bin:$PATH" \
    PYTHONUNBUFFERED=1 \
    TZ=Europe/Rome \
    DJANGO_SETTINGS_MODULE=settings.docker 

# Source
COPY . /src
COPY --from=builder /src/.venv /src/.venv
RUN    chmod 755 /src/manage.py \
    && chmod 755 /src/docker/entrypoint.sh \
    && sync \
    && /src/manage.py collectstatic --link --noinput --verbosity=0

WORKDIR /src/
VOLUME ["/var/tmp/nginx"]

EXPOSE 8000

ENTRYPOINT ["/src/docker/entrypoint.sh"]
# CMD ["uwsgi", "--master", "--processes", "4", "--threads", "8", "--chdir", "/src", "--wsgi", "settings.wsgi", "--http-socket", ":8000", "--stats", ":9191"]
CMD ["gunicorn", "--workers", "4", "--threads", "1", "--worker-class", "sync", "--timeout", "60", "--graceful-timeout", "30", "--chdir", "/src", "settings.wsgi:application", "--bind", "0.0.0.0:8000", "--access-logfile", "-", "--error-logfile", "-"]
