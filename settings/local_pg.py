from .test import *
from dotenv import load_dotenv


DEBUG=True

load_dotenv('credentials.env')

MASTERCOM_URL = os.getenv('MASTERCOM_URL')

NEXT_API_USER = os.getenv('NEXT_API_USER')
NEXT_API_PASSWORD = os.getenv('NEXT_API_PASSWORD')

SET_DB_FROM_PARAMETRI = True


NEXT_API_URL = 'https://demo4.registroelettronico.com'

DATABASES['default'] = {
    'ENGINE': 'django.db.backends.postgresql_psycopg2',
    'NAME': 'mastercom2',
    'USER': os.getenv('DB_USER'),
    'PASSWORD': os.getenv('DB_PASSWORD'),
    'HOST': '127.0.0.1',
    'PORT': '5432',
}


DATABASES['db_mastercom'] = {
    'ENGINE': 'django.db.backends.postgresql_psycopg2',
    'NAME': 'mastercom_2024_2025',
    'USER': os.getenv('DB_USER'),
    'PASSWORD': os.getenv('DB_PASSWORD'),
    'HOST': '127.0.0.1',
    'PORT': '5432',
}

DATABASES['db_parametri'] = {
    'ENGINE': 'django.db.backends.postgresql_psycopg2',
    'NAME': 'parametri',
    'USER': os.getenv('DB_USER'),
    'PASSWORD': os.getenv('DB_PASSWORD'),
    'HOST': '127.0.0.1',
    'PORT': '5432',
}
