from django.conf import settings


class ProdRouter:

    not_managed = ["cashflow", "common"]

    def db_for_read(self, model, **hints):
        return "default"

    def db_for_write(self, model, **hints):
        return "default"

    def allow_relation(self, obj1, obj2, **hints):
        return True

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        if app_label in self.not_managed:
            return False
        return True