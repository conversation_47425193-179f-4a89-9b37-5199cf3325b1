services:
  db:
    image: postgres:9.5-alpine
    environment:
      - POSTGRES_PASSWORD=postgres
    volumes:
      - db:/var/lib/postgresql/data

  django:
    build: .
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=settings.test_docker
    depends_on:
      - "db"
    links:
      - "db"

  nginx:
    build: .
    depends_on:
      - "django"
    links:
      - "django:django"
    command: nginx


volumes:
  db:
    driver: local
