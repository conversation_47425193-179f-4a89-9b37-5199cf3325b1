from datetime import date

from .validators import ColumnRequiredValidator, ColumnLengthValidator, ColumnTypeValidator

class ColumnFormatter:

    def __init__(self, range: list[int], align: str='left', fill_char: str=' '):
        if range[0]>range[1]:
            raise ValueError('Start must be less than end')
        if align not in ['left', 'right']:
            raise ValueError('Align must be left or right')
        if fill_char is None:
            raise ValueError('Fill char must be a character')

        self.start = range[0]
        self.end = range[1]
        self.align = align
        self.fill_char = fill_char
        self.length = self.end - self.start + 1

    def align_text(self, value):
        return f"{value:<{self.length}}" if self.align == 'left' else f"{value:>{self.length}}"

    def render(self, value):
        return self.align_text(value).replace(' ', self.fill_char)


class BaseColumn:

    validators_cls = [ColumnRequiredValidator, ColumnLengthValidator, ColumnTypeValidator]
    allowed_types = (str, int, float, date)

    def __init__(self, description: str=None, required: bool=False, default=None,  range: list=None, align: str='left', fill_char: str=' '):
        self.description = description
        self.required = required if required is not None else False
        self.default = default

        self.formatter = ColumnFormatter(range, align, fill_char) if range is not None else None
        self.validators = [validator(column=self) for validator in self.validators_cls]

    def get_validators(self):
        return self.validators

    def validate(self, value):
        for validator in self.get_validators():
            validator.validate(value)

    def cast(self, value=None):
        if not value:
            return '' if not self.default else str(self.default)
        return str(value)

    def render(self, value=None):
        self.validate(value)
        return self.formatter.render(self.cast(value)) if self.formatter is not None else self.cast(value)


class TextColumn(BaseColumn):
    allowed_types = (str, )


class NumericColumn(BaseColumn):
    allowed_types = (int, float )


class MonetaryColumn(NumericColumn):
    pass


class DateColumn(BaseColumn):
    allowed_types = (date, )

    def __init__(self, format: str='%Y-%m-%d', **kwargs):
        super().__init__(**kwargs)
        self.format = format

    def cast(self, value: date):
        return value.strftime(self.format)
