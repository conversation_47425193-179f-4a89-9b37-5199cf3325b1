
class Renderer:

    def __init__(self, separator: str = ';', header: bool = True, lineterminator: str = '\n', **kwargs):
        self.separator = separator or ';'
        self.header = header if header is not None else True
        self.lineterminator = lineterminator or '\n'

class CsvRenderer(Renderer):

    def render(self, rows: list[dict]) -> str:
        result = ''
        if len(rows) == 0:
            return result
        if self.header:
            result += self.separator.join(rows[0].keys()) + '\n'

        for row in rows:
            result += self.separator.join(row.values()) + '\n'
        return result


class TxtRenderer(Renderer):

    def render(self, rows: list[dict]) -> str:
        result = ''
        for row in rows:
            result += ''.join(row.values()) + self.lineterminator
        return result

