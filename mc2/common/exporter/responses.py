from django.http import FileResponse

from .base import Serializer
from .renderers import Csv<PERSON><PERSON><PERSON>, TxtRenderer

class ExportResponse(FileResponse):
    content_type=None
    renderer_cls=None

    def __init__(self, export: Serializer, **kwargs):
        if not isinstance(export, Serializer):
            raise ValueError('export must be an instance of Exporter')

        self.renderer = self.renderer_cls(
            **{
                'separator': export.separator,
                'header': export.header,
                'lineterminator': export.lineterminator
            }
        )

        self.raw_content = self.renderer.render(export.format())

        super().__init__(
            self.raw_content,
            as_attachment=True,
            filename=export.get_filename(),
            content_type=self.content_type
        )

        self['Content-Disposition'] = f'attachment; filename="{self.filename}"'


class CsvExportResponse(ExportResponse):
    content_type = 'text/csv'
    renderer_cls=CsvRenderer


class TxtExportResponse(ExportResponse):
    content_type = 'text/plain'
    renderer_cls=TxtRenderer
