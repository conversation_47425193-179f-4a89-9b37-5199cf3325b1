from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework import serializers

from . import responses


class ExportAPIView(APIView):
    RESPONSES_MAP =  {
        'csv': responses.CsvExportResponse,
        'txt': responses.TxtExportResponse
    }
    serializer_cls = None
    filter_form_cls = None

    def validate_filters(self, params):
        if not self.filter_form_cls:
            return params
        filter = self.filter_form_cls(params)
        if not filter.is_valid():
            raise serializers.ValidationError('Parameters are not valid', filter.errors)
        return filter.cleaned_data

    def serialize(self, data):
        serializer = self.serializer_cls(data)
        if not serializer.validate():
            raise serializers.ValidationError('Parameters are not valid', serializer.errors)
        return serializer

    def get(self, request, *args, **kwargs):
        params = self.validate_filters(request.GET)
        data_dict = self.build(params)
        return self.export(self.serialize(data_dict), request.GET.get('fmt', 'csv'))

    def export(self, data, format = 'csv'):
        return self.RESPONSES_MAP.get(format)(data)

    def build(self, params):
        """
        This method should be overridden in subclasses to implement the logic for building the data.
        """
        raise NotImplementedError("Subclasses must implement this method.")