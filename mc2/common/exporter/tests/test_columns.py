from datetime import date

from django.test import TestCase

from ..columns import BaseColumn,  TextColumn, NumericColumn, MonetaryColumn, DateColumn

class ColumnFormatterTest(TestCase):

    def test_end_grather_than_start_range(self):
        with self.assertRaises(ValueError) as e:
            BaseColumn(range=[5, 1])
        self.assertEqual(str(e.exception), 'Start must be less than end')


class BaseColumnTest(TestCase):

    def test_init_with_no_params_ok(self):
        base_column = BaseColumn()
        self.assertIsNone(base_column.description)
        self.assertIsNone(base_column.formatter)

    def test_init_with_no_params_but_positional_data_ok(self):
        base_column = BaseColumn(range=[1, 5], fill_char=' ', align='left')
        self.assertEqual(base_column.formatter.align, 'left')
        self.assertEqual(base_column.formatter.fill_char, ' ')
        self.assertEqual(base_column.formatter.start, 1)
        self.assertEqual(base_column.formatter.end, 5)

    def test_validate_no_required(self):
        base_column = BaseColumn()
        self.assertIsNone(base_column.validate('value'))

    def test_validate_required(self):
        base_column = BaseColumn(required=True)
        with self.assertRaises(ValueError):
            base_column.validate(None)

    def test_render_base_column_ok(self):
        base_column = BaseColumn()
        self.assertEqual(base_column.render('value'), 'value')

    def test_render_position_column_ok(self):
        base_column = BaseColumn(range=[1, 7], fill_char=' ')
        self.assertEqual(base_column.render('value'), 'value  ')

    def test_render_align_column_ok(self):
        base_column = BaseColumn(range=[1, 7], fill_char=' ', align="right")
        self.assertEqual(base_column.render('value'), '  value')

    def test_position_validate_exeede_length(self):
        base_column = BaseColumn(range=[1, 5], fill_char=' ', align="right")
        with self.assertRaises(ValueError):
            base_column.validate('textistoolong')

    def test_position_validate_no_align(self):
        with self.assertRaises(ValueError) as e:
            BaseColumn(range=[1, 5], fill_char=' ', align=None)
        self.assertEqual(str(e.exception), 'Align must be left or right')

    def test_position_validate_None_fillchar(self):
        with self.assertRaises(ValueError) as e:
            BaseColumn(range=[1, 5], fill_char=None, align='left')
        self.assertEqual(str(e.exception), 'Fill char must be a character')

    def test_cast_with_None_passing(self):
        base_column = BaseColumn()
        self.assertEqual(base_column.cast(None), '')

    def test_cast_with_None_passing_and_default(self):
        base_column = BaseColumn(default='default')
        self.assertEqual(base_column.cast(None), 'default')

    def test_cast_with_value_passing_and_default(self):
        base_column = BaseColumn(default='default')
        self.assertEqual(base_column.cast('value'), 'value')

    def test_render_default_column_with_formatter_ok(self):
        base_column = BaseColumn(range=[1, 5], fill_char='*', align='right')
        self.assertEqual(base_column.render('T'), '****T')


class TextColumnTest(TestCase):

    def test_render_default(self):
        text_column = TextColumn()
        self.assertEqual(text_column.render('value'), 'value')

    def test_render_position(self):
        text_column = TextColumn(range=[1, 10])
        self.assertEqual(text_column.render('value'), 'value     ')

    def test_render_aligned(self):
        text_column = TextColumn(range=[1, 10], align="right")
        self.assertEqual(text_column.render('value'), '     value')

    def test_render_align_right_align_fillchar(self):
        text_column = TextColumn(range=[1, 10], align="right", fill_char='*')
        self.assertEqual(text_column.render('value'), '*****value')

    def test_render_align_left_align_fillchar(self):
        text_column = TextColumn(range=[1, 10], fill_char='*')
        self.assertEqual(text_column.render('value'), 'value*****')

    def test_valdation_error_passing_int(self):
        text_column = TextColumn()
        with self.assertRaises(ValueError):
            text_column.validate(12)

    def test_render_range_and_rest_as_default(self):
        text_column = TextColumn(range=[1, 5])
        self.assertEqual(text_column.render(), '     ')




class NumericColumnTest(TestCase):

    def test_render_default(self):
        text_column = NumericColumn()
        self.assertEqual(text_column.render(12), '12')
        self.assertEqual(text_column.render(12.2), '12.2')

    def test_valdation_error_passing_string(self):
        text_column = NumericColumn()
        with self.assertRaises(ValueError):
            text_column.validate('12')


class MoneyColumnTest(TestCase):

    def test_render_default(self):
        text_column = MonetaryColumn()
        self.assertEqual(text_column.render(12.5), '12.5')


class DateColumnTest(TestCase):

    def test_init_default(self):
        col = DateColumn()
        self.assertEqual(col.format, '%Y-%m-%d')

    def test_init_custom(self):
        col = DateColumn(format='%d/%m/%Y')
        self.assertEqual(col.format, '%d/%m/%Y')

    def test_validate_ok(self):
        col = DateColumn(format='%d/%m/%Y')
        self.assertIsNone(col.validate(date(2020, 1, 1)))

    def test_validate_type_error(self):
        col = DateColumn(format='%d/%m/%Y')
        with self.assertRaises(ValueError) as e:
            col.validate('BAD DATE')
        self.assertEqual(str(e.exception), '{} is not a valid type')

    def test_validate_format_length_error(self):
        col = DateColumn(format='%Y%m%d', range=[1, 4])
        with self.assertRaises(ValueError) as e:
            col.validate(date(2020, 1, 1))
        self.assertEqual(str(e.exception), '{} exceeds the length')

    def test_validate_format_length_ok(self):
        col = DateColumn(format='%Y%m%d', range=[1, 8])
        self.assertIsNone(col.validate(date(2020, 1, 1)))

    def test_render_default(self):
        col = DateColumn()
        self.assertEqual(col.render(date(2020, 1, 1)), '2020-01-01')

    def test_render_custom(self):
        col = DateColumn(format='%Y%m%d')
        self.assertEqual(col.render(date(2020, 1, 1)), '20200101')

