import unittest

from mc2.common.exporter.renderers import CsvRenderer, TxtRenderer


class TestCsvRenderer(unittest.TestCase):

    def test_to_string_with_default_init(self):
        renderer = CsvRenderer()
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': '<PERSON>', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), 'name;age\nAlice;30\nBob;25\n')

    def test_to_string_with_header(self):
        renderer = CsvRenderer(separator=',', header=True)
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': '<PERSON>', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), 'name,age\nAlice,30\nBob,25\n')

    def test_to_string_without_header(self):
        renderer = CsvRenderer(separator=',', header=False)
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': '<PERSON>', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), '<PERSON>,30\nBob,25\n')

    def test_to_string_with_custom_separator(self):
        renderer = CsvRenderer(separator='|', header=True)
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': 'Bob', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), 'name|age\nAlice|30\nBob|25\n')



class TestTxtRenderer(unittest.TestCase):

    def test_to_string_with_default_lineterminator(self):
        renderer = TxtRenderer()
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': 'Bob', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), 'Alice30\nBob25\n')

    def test_to_string_with_custom_lineterminator(self):
        renderer = TxtRenderer(lineterminator='|')
        rows = [
            {'name': 'Alice', 'age': '30'},
            {'name': 'Bob', 'age': '25'}
        ]
        self.assertEqual(renderer.render(rows), 'Alice30|Bob25|')



