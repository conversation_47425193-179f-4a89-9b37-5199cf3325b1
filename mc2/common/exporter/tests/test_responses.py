from django.test import TestCase

from ..columns import TextColumn
from ..responses import TxtExportResponse, CsvExportResponse, ExportResponse
from ..base import Serializer


class ExportResponseTest(TestCase):
    def test_init_default(self):
        with self.assertRaises(ValueError):
            ExportResponse({})


class TxtExportResponseTest(TestCase):

    def test_init_default(self):
        class TestExport(Serializer):
            name = TextColumn()

        export = TestExport([{'name': 'John'}])

        response = TxtExportResponse(export)
        self.assertEqual(response.renderer.lineterminator, '\n')
        self.assertEqual(response.renderer.separator, ';')
        self.assertEqual(response.content_type, 'text/plain')
        self.assertIsNotNone(response.filename)
        self.assertTrue(response.renderer.header)

    def test_init_custom_lineterminator(self):
        class TestExport(Serializer):
            name = TextColumn()

            lineterminator = '->'

        export = TestExport([{'name': '<PERSON>'}])

        response = TxtExportResponse(export)
        self.assertEqual(response.renderer.lineterminator, '->')

    def test_export_ok(self):
        class TestExport(Serializer):
            a = TextColumn(range=(1, 3), fill_char=' ', align='right')
            b = TextColumn(range=(4, 7), fill_char='0')

            lineterminator = '->'
            filename = 'file'

        export = TestExport([
            {'a': '1', 'b': '2'},
            {'a': '3', 'b': '4'},
        ])

        response = TxtExportResponse(export)
        self.assertTrue(response.as_attachment)
        self.assertEqual(response.filename, 'file')
        self.assertEqual(response.content_type, 'text/plain')



class CsvExportResponseTest(TestCase):

    def test_init_default(self):
        class TestExport(Serializer):
            name = TextColumn()

        export = TestExport([{'name': 'John'}])

        response = CsvExportResponse(export)
        self.assertEqual(response.content_type, 'text/csv')
        self.assertIsNotNone(response.filename)
        self.assertEqual(response.renderer.separator, ';')
        self.assertTrue(response.renderer.header)
        self.assertEqual(response.renderer.lineterminator, '\n')

    def test_init_custom_linedelimiter(self):
        class TestExport(Serializer):
            name = TextColumn()

            separator = ','
            header = False

        export = TestExport([{'name': 'John'}])

        response = CsvExportResponse(export)
        self.assertEqual(response.renderer.separator, ',')
        self.assertFalse(response.renderer.header)

    def test_export_header_ok(self):
        class TestExport(Serializer):
            a = TextColumn(range=(1, 3), fill_char=' ', align='right')
            b = TextColumn(range=(4, 7), fill_char='0')

            filename='file.csv'

        export = TestExport([
            {'a': '1', 'b': '2'},
            {'a': '3', 'b': '4'},
        ])

        response = CsvExportResponse(export)
        self.assertTrue(response.as_attachment)
        self.assertEqual(response.renderer.separator, ';')
        self.assertEqual(response.filename, 'file.csv')
        self.assertEqual(response.content_type, 'text/csv')


