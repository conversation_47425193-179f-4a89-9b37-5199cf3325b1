from django.test import TestCase

from ..columns import TextColumn, NumericColumn

from ..base import Serializer, ExportsConfig


class ExporterTest(TestCase):

    def test_init_columns_generations(self):
        class MyTestExport(Serializer):
            name = TextColumn()
            age = NumericColumn()

        exporter = MyTestExport()
        self.assertEqual(len(exporter.columns), 2)
        self.assertEqual(type(exporter.columns['name']), TextColumn)
        self.assertEqual(type(exporter.columns['age']), NumericColumn)

    def test_validate_no_field_required(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True)
            age = NumericColumn()

        exporter = MyTestExport([{'age': 30}])

        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 1)
        self.assertEqual(exporter.errors[0], {'name': 'name is required'})

    def test_validate_None_required(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True)
            age = NumericColumn()

        exporter = MyTestExport([{'name': None, 'age': 30}])

        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 1)
        self.assertEqual(exporter.errors[0], {'name': 'name is required'})

    def test_validate_type_error(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True)

        exporter = MyTestExport([{'name': 30}])
        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 1)
        self.assertEqual(exporter.errors[0], {'name': 'name is not a valid type'})

    def test_validate_ko_errors_per_row(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True)
            age = NumericColumn(required=True)

        exporter = MyTestExport([
            {'name': 'Name'},
            {'age': 30},
            {},
        ])

        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 3)

    def test_render_with_align_right_and_fillchar_ok(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0', align="right")

        exporter = MyTestExport([{'name': 'John'}])
        self.assertTrue(exporter.validate())
        self.assertEqual(exporter.format(), [{'name': '0John'}])

    def test_render_with_align_left_and_fillchar_ok(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0')

        exporter = MyTestExport([{'name': 'John'}])
        self.assertEqual(exporter.format(), [{'name': 'John0'}])

    def test_add_multiple_row_with_align_and_fillchar_ok(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0', align="right")

        exporter = MyTestExport([
            {'name': 'John'},
            {'name': 'Jenny'},
        ])
        self.assertEqual(len(exporter.rows), 2)
        render = exporter.format()
        self.assertEqual(render[0], {'name': '0John'})
        self.assertEqual(render[1], {'name': 'Jenny'})

    def test_data_ok(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0', align="right")

        exporter = MyTestExport([{'name': 'John'}, {'name': 'Jenny'}])

        self.assertEqual(len(exporter.rows), 2)
        self.assertTrue(exporter.validate())
        result = exporter.format()
        self.assertEqual(result[0], {'name': '0John'})
        self.assertEqual(result[1], {'name': 'Jenny'})

    def test_add_row_errors_max_length(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0', align="right")

        exporter = MyTestExport([{'name': 'ErrorInLenghtGratherThan5'}])
        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 1)
        self.assertEqual(exporter.errors[0], {'name': 'name exceeds the length'})

    def test_data_multiple_errors(self):
        class MyTestExport(Serializer):
            name = TextColumn(required=True, range=[1, 5], fill_char='0', align="right")

        exporter = MyTestExport([
                {'name': 'John'},
                {'name': 'ErrorInLenghtGratherThan5'},
                {'name': 'Jenny'},
                {'name': None},
                {'name': 'Tom'},
                ])


        self.assertFalse(exporter.validate())
        self.assertEqual(len(exporter.errors), 5)
        self.assertEqual(exporter.errors[0], {})
        self.assertEqual(exporter.errors[1], {'name': 'name exceeds the length'})
        self.assertEqual(exporter.errors[2], {})
        self.assertEqual(exporter.errors[3], {'name': 'name is required'})
        self.assertEqual(exporter.errors[4], {})


class TestExportsConfig(TestCase):

    def test_init_ok(self):
        config = ExportsConfig()
        self.assertEqual(config.exports, {})

    def test_add_ok(self):
        class MyExporter(Serializer):
            pass

        config = ExportsConfig()
        config.add(MyExporter, 'example_export')
        self.assertEqual(config.exports, {'example_export': MyExporter})

    def test_add_raise_error(self):
        class MyExporter(Serializer):
            pass

        config = ExportsConfig()
        config.add(MyExporter, 'example_export')
        with self.assertRaises(ValueError):
            config.add(MyExporter, 'example_export')

    def test_get_ok(self):
        class MyExporter(Serializer):
            pass

        config = ExportsConfig()
        config.add(MyExporter, 'example_export')
        self.assertEqual(config.get('example_export'), MyExporter)
        self.assertIsNone(config.get('not_exist'))