from django.test import TestCase
from django.forms import Form
from django.forms.fields import <PERSON><PERSON><PERSON><PERSON>
from rest_framework import serializers


from ..views import ExportAPIView
from mc2.common.exporter.columns import TextColumn
from mc2.common.exporter.base import Serializer




class ExportAPIViewTest(TestCase):

    def test_validate_filter_no_filter_cls(self):
        view = ExportAPIView()
        params = {'param1': 'value1', 'param2': 'value2'}
        self.assertEqual(view.validate_filters(params), params)

    def test_validate_filter_error_in_passing_params(self):
        class TestForm(Form):
            name = CharField(required=True)

        view = ExportAPIView()
        view.filter_form_cls = TestForm
        with self.assertRaises(serializers.ValidationError):
            view.validate_filters({'other_param': 'other_value'})

    def test_build_not_implemented(self):
        view = ExportAPIView()
        with self.assertRaises(NotImplementedError):
            view.build({})

    def test_serialise_raise_exception(self):
        class TestSerializer(Serializer):
            name = TextColumn(required=True)

        view = ExportAPIView()
        view.serializer_cls = TestSerializer
        with self.assertRaises(serializers.ValidationError):
            view.serialize([{'bad_param': 'bad_value'}])
