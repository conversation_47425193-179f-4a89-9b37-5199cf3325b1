from datetime import date

from django.test import TestCase

from ..columns import BaseColumn,  TextColumn, NumericColumn, MonetaryColumn, DateColumn
from ..validators import ColumnRequiredValidator, ColumnLengthValidator, ColumnTypeValidator


class ColumnRequiredValidatorTest(TestCase):

    def test_validate_column_required_ok(self):
        self.assertIsNone(ColumnRequiredValidator(TextColumn()).validate())

    def test_validate_column_not_required_ok(self):
        self.assertIsNone(ColumnRequiredValidator(TextColumn(required=True)).validate('value'))

    def test_validate_column_required_with_default(self):
        self.assertIsNone(ColumnRequiredValidator(TextColumn(required=True, default='value')).validate())

    def test_validate_column_required_with_default_but_int_0_as_value(self):
        self.assertIsNone(ColumnRequiredValidator(NumericColumn(required=True, default=0)).validate())

    def test_validate_column_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnRequiredValidator(TextColumn(required=True)).validate(None)

        self.assertEqual(str(e.exception), '{} is required')

    def test_validate_max_length_ok(self):
        self.assertIsNone(ColumnLengthValidator(TextColumn(range=[1, 5])).validate('value'))

    def test_validate_max_length_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnLengthValidator(TextColumn(range=[1, 5])).validate('textistoolong')

        self.assertEqual(str(e.exception), '{} exceeds the length')

    def test_validate_type_ok(self):
        self.assertIsNone(ColumnTypeValidator(TextColumn()).validate('value'))

    def test_validate_type_str_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnTypeValidator(TextColumn()).validate(30)

        self.assertEqual(str(e.exception), '{} is not a valid type')

    def test_validate_type_int_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnTypeValidator(NumericColumn()).validate('value')

        self.assertEqual(str(e.exception), '{} is not a valid type')

    def test_validate_type_float_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnTypeValidator(NumericColumn()).validate('value')

        self.assertEqual(str(e.exception), '{} is not a valid type')

    def test_validate_type_int_ok(self):
        self.assertIsNone(ColumnTypeValidator(NumericColumn()).validate(30))

    def test_validate_type_float_ok(self):
        self.assertIsNone(ColumnTypeValidator(NumericColumn()).validate(30.0))

    def test_validate_type_date_ok(self):
        self.assertIsNone(ColumnTypeValidator(DateColumn()).validate(date.today()))

    def test_validate_type_date_error(self):
        with self.assertRaises(ValueError) as e:
            ColumnTypeValidator(DateColumn()).validate('value')

        self.assertEqual(str(e.exception), '{} is not a valid type')

    def test_validate_type_with_value_None(self):
        self.assertIsNone(ColumnTypeValidator(TextColumn()).validate(None))
        self.assertIsNone(ColumnTypeValidator(NumericColumn()).validate(None))
        self.assertIsNone(ColumnTypeValidator(DateColumn()).validate(None))
        self.assertIsNone(ColumnTypeValidator(MonetaryColumn()).validate(None))

    def test_validate_length_with_value_None(self):
        self.assertIsNone(ColumnLengthValidator(TextColumn()).validate(None))
        self.assertIsNone(ColumnLengthValidator(NumericColumn()).validate(None))
        self.assertIsNone(ColumnLengthValidator(DateColumn()).validate(None))
        self.assertIsNone(ColumnLengthValidator(MonetaryColumn()).validate(None))