import uuid

from .columns import BaseColumn

class Serializer:
    separator = None
    header = None
    lineterminator = None
    filename  = None

    def __init__(self, data=None, *args, **kwargs):
        """_summary_

        Args:
            data (_type_, optional): _description_. Defaults to None.
        """        
        self.columns = {name: column for name, column in vars(self.__class__).items() if isinstance(column, BaseColumn)}
        self.rows = []
        self.errors = []

        if data:
            for row in data:
                self.rows.append(row)

    def validate(self):
        """_summary_

        Returns:
            _type_: _description_
        """        
        self.errors = []
        error = False
        for row in self.rows:
            row_errors = {}
            for column_name, column in self.columns.items():
                try:
                    column.validate(row.get(column_name, None))
                except ValueError as e:
                    error = True
                    row_errors[column_name] = str(e).format(column_name)
            self.errors.append(row_errors)

        if not error:
            self.errors = []

        return len(self.errors) == 0

    def format(self) -> list[dict]:
        result = []
        for row in self.rows:
            rendered_row = {column_name: column.render(row.get(column_name, None)) for column_name, column in self.columns.items()}
            result.append(rendered_row)
        return result

    def get_filename(self) -> str:
        return uuid.uuid4().hex if not self.filename else self.filename


class ExportsConfig:

    def __init__(self):
        self.exports = {}

    def add(self, exporter: Serializer, name: str):
        if name in self.exports:
            raise ValueError(f'Exporter {name} already exists')
        self.exports[name] = exporter

    def get(self, name) -> Serializer:
        return self.exports.get(name, None)


exports = ExportsConfig()