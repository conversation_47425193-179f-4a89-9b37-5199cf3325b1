from datetime import date


class ColumnBaseValidator:
    def __init__(self, column):
        self.column = column


class ColumnRequiredValidator(ColumnBaseValidator):
    def validate(self, value=None):
        if value is None and self.column.required is True and self.column.default is None:
            raise ValueError('{} is required')


class ColumnLengthValidator(ColumnBaseValidator):
    def validate(self, value=None):
        if self.column.formatter is not None and len(self.column.cast(value)) > self.column.formatter.length:
            raise ValueError('{} exceeds the length')


class ColumnTypeValidator(ColumnBaseValidator):

    def validate(self, value=None):
        if value and not isinstance(value, self.column.allowed_types):
            raise ValueError('{} is not a valid type')