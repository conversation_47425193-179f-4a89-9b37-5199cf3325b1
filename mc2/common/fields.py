from datetime import datetime

from django.utils.translation import gettext_lazy as _
from django.forms import DateTimeField
from django.contrib.admin.widgets import AdminDateWidget


class UnixTimestampWidget(AdminDateWidget):

    def format_value(self, value):
        value = datetime.fromtimestamp(int(value)).date()
        return super().format_value(value)


class UnixTimestampField(DateTimeField):
    widget = UnixTimestampWidget
    default_error_messages = {
        'invalid': _('Enter a valid date.'),
    }

    def to_python(self, value):
        try:
            value = datetime.fromtimestamp(value)
        except:
            pass
        return super().to_python(value)

    def clean(self, value):
        return int(super().clean(value).timestamp())
