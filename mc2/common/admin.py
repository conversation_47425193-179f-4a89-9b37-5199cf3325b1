from django.contrib import admin

from .models import Parameter

@admin.register(Parameter)
class ParameterAdmin(admin.ModelAdmin):
    list_display = ('parameter_id', 'name', 'value')
    search_fields = ('name', 'value')
    list_filter = ('name', 'value')
    ordering = ('name', 'value')
    fieldsets = (
        (None, {
            'fields': ('name', 'value')
        }),
    )
    def __str__(self):
        return self.name