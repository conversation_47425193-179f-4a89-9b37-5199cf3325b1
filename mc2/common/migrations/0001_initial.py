# Generated by Django 3.1.14 on 2024-12-18 16:01

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Parameter',
            fields=[
                ('parameter_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('value', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'parameter',
            },
        ),
    ]
