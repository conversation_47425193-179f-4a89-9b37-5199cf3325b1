from django.test import TestCase

from ..utils import get_institute, format_name
from ..models import Institute
from.factories import InstituteFactory

class UtilsTest(TestCase):

    def test_get_current_institute_no_institute(self):
        self.assertIsNone(get_institute())

    def test_get_current_institute_ok(self):
        InstituteFactory(def_field=True)
        self.assertIsInstance(get_institute(), Institute)

    def test_format_name(self):
        self.assertEqual(format_name(''), '')
        self.assertEqual(format_name('test'), 'Test')
        self.assertEqual(format_name('test test'), 'Test Test')
        self.assertEqual(format_name('TEST TEST'), 'Test Test')
        self.assertEqual(format_name('test test test'), 'Test Test Test')
        self.assertEqual(format_name('test test test test'), 'Test Test Test Test')
        self.assertEqual(format_name('test test test test test'), 'Test Test Test Test Test')