import factory

from ..models import Parameter, Institute


class ParameterFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Parameter

    name = factory.Sequence(lambda n: 'Parameter %s' % n)
    value = 'Value'


class InstituteFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Institute

    name = 'Test institute'
    fiscal_code = '12345678901'
    school_fiscal_code = '12345678902'
    dir_birth = 1
    dir_emp_id = 1
    adir_emp_id = 1
    presge_emp_id = 1
    segcons_emp_id = 1
    prescon_emp_id = 1
    respacq_emp_id = 1
    def_field = True