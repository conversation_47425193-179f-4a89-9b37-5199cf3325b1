from django.test import TestCase

from .factories import ParameterFactory, InstituteFactory

class ParameterModelTest(TestCase):
    def test_parameter_str(self):
        parameter = ParameterFactory(name='Test Parameter')
        self.assertEqual(parameter.__str__(), 'Test Parameter')


    def test_parameter_value(self):
        parameter = ParameterFactory(name='Parameter Name', value='Parameter Value')
        self.assertEqual(parameter.value, 'Parameter Value')
        self.assertEqual(parameter.name, 'Parameter Name')


class InstituteModelTest(TestCase):

    def test_institute_str(self):
        institute = InstituteFactory(name='Test Institute')
        self.assertEqual(institute.__str__(), 'Test Institute')