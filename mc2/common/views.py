from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from django.http import HttpResponse

def PrometheusMetrics(request):
    """_summary_

    Args:
        request (_type_): _description_

    Returns:
        _type_: _description_
    """
    return HttpResponse(generate_latest(), content_type=CONTENT_TYPE_LATEST)


def GenericOk(request):
    """Generic OK response for health checks."""
    return HttpResponse("OK", content_type="text/plain")