from django.db import models


class Parameter(models.Model):
    parameter_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    value = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'parameter'

    def __str__(self):
        return self.name


class Regions(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    name = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'regions'


class Cities(models.Model):
    city_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=50)
    city_code = models.CharField(max_length=10, blank=True, null=True)
    province = models.CharField(max_length=5, blank=True, null=True)
    region = models.ForeignKey(Regions, models.DO_NOTHING, db_column='region', blank=True, null=True)
    is_city = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        db_table = 'cities'


class Contact(models.Model):
    contact_id = models.AutoField(primary_key=True)
    address = models.TextField(blank=True, null=True)
    phone_num = models.TextField(blank=True, null=True)
    fax = models.TextField(blank=True, null=True)
    city = models.ForeignKey(Cities, models.DO_NOTHING, blank=True, null=True)
    email = models.TextField(blank=True, null=True)
    mobile = models.TextField(blank=True, null=True)
    web = models.TextField(blank=True, null=True)
    cap = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        db_table = 'contact'


class Institute(models.Model):
    institute_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    mechan_code = models.CharField(max_length=16)
    contact = models.OneToOneField(Contact, models.DO_NOTHING, blank=True, null=True)
    fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    school_type = models.CharField(max_length=3, blank=True, null=True)
    parent = models.IntegerField(blank=True, null=True)
    def_field = models.BooleanField(db_column='def', blank=True, null=True)  # Field renamed because it was a Python reserved word.
    dir_name = models.CharField(max_length=100, blank=True, null=True)
    dir_surname = models.CharField(max_length=100, blank=True, null=True)
    adir_name = models.CharField(max_length=100, blank=True, null=True)
    adir_surname = models.CharField(max_length=100, blank=True, null=True)
    pres_ge_name = models.CharField(max_length=100, blank=True, null=True)
    pres_ge_surname = models.CharField(max_length=100, blank=True, null=True)
    seg_cons_name = models.CharField(max_length=100, blank=True, null=True)
    seg_cons_surname = models.CharField(max_length=100, blank=True, null=True)
    pres_con_name = models.CharField(max_length=100, blank=True, null=True)
    pres_con_surname = models.CharField(max_length=100, blank=True, null=True)
    dir_fiscal_code = models.TextField(blank=True, null=True)
    school_fiscal_code = models.TextField(blank=True, null=True)
    inpdap_code = models.TextField(blank=True, null=True)
    assicurazioni_sanitarie = models.TextField(blank=True, null=True)
    dir_sesso = models.CharField(max_length=1)
    dir_birth = models.BigIntegerField()
    dir_city = models.TextField()
    postal_account = models.BigIntegerField(blank=True, null=True)
    ateco_code = models.TextField(blank=True, null=True)
    activity_code = models.TextField(blank=True, null=True)
    dir_curr_addr = models.TextField()
    dir_curr_city = models.TextField()
    dir_curr_phone = models.TextField()
    dir_emp_id = models.IntegerField() # per non stare ad importare Employee. Originale => models.ForeignKey(Employee, models.DO_NOTHING, blank=True, null=True)
    adir_emp_id = models.IntegerField()
    presge_emp_id = models.IntegerField()
    segcons_emp_id = models.IntegerField()
    prescon_emp_id = models.IntegerField()
    respacq_emp_id = models.IntegerField()
    job_director_id = models.IntegerField(blank=True, null=True)
    job_vice_director_id = models.IntegerField(blank=True, null=True)
    job_dsga_id = models.IntegerField(blank=True, null=True)
    job_personnel_id = models.IntegerField(blank=True, null=True)
    job_accounting_id = models.IntegerField(blank=True, null=True)
    job_warehouse_id = models.IntegerField(blank=True, null=True)
    job_registry_id = models.IntegerField(blank=True, null=True)
    ipa_code = models.CharField(max_length=255, blank=True, null=True)
    ae_fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    ade_email = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'institute'

    def __str__(self):
        return self.name
