import requests

from django.conf import settings


class NextApi:
    url = settings.NEXT_API_URL
    token = None

    def set_token(self, token):
        self.token = token

    def get(self, endpoint, params=None):
        if not self.token:
            raise Exception('Token is mandatory')
        res = requests.get(self.url + endpoint, params=params, headers={'Authorization': self.token})
        if res.status_code != 200:
            raise Exception('Error calling NextApi: ' + res.text)
        return res.json()

    def login(self, username, password):
        res = requests.post(self.url + '/next-api/v1/login', json={'username': username, 'password': password})
        if res.status_code != 200:
            raise Exception('Error calling NextApi login: ' + res.text)
        self.token = res.text
        return True
