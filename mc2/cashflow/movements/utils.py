from mc2.cashflow.models.movement import CcpMovement
from mc2.cashflow.models.type import CcpType
from mc2.cashflow.models.credit import CcpCredits, CcpCreditsType
from mc2.common.utils import format_name

def create(data, *args, **kwargs):
    m = CcpMovement()
    m.amount = data.get('amount', None)
    m.school_year = data.get('school_year', None)
    m.type = data.get('type', None)
    m.creation_date = data.get('creation_date', None)
    m.expiration_date = data.get('expiration_date', None)
    m.subject_id = data.get('subject_id', None)
    m.subject_data = format_name(data.get('subject_data', None))
    m.subject_seat = data.get('subject_seat', None)
    m.subject_class = data.get('subject_class', None)
    m.subject_type = data.get('subject_type', None)
    m.subject_school_year = data.get('subject_school_year', None)
    m.subject_school_address_code = data.get('subject_school_address_code', None)
    m.subject_school_address = data.get('subject_school_address', None)
    m.payment_intent_token = data.get('payment_intent_token', None)
    m.payment_intent_id = data.get('payment_intent_id', None)
    m.locked = data.get('locked', None)
    m.save()
    # TODO add log
    createSubjectCredit(m)

    return m


def createSubjectCredit(movement, *args, **kwargs):

    type = CcpType.objects.get(id=movement.type.id)

    if type.ccp_credits_type:
        credit_type = CcpCreditsType.objects.get(id=type.ccp_credits_type)
        if credit_type:
            subject_credit = CcpCredits.objects.filter(
                subject_id=movement.subject_id,
                subject_type=movement.subject_type,
                credit_type=credit_type.id
            )
            if not credit_type.movement:
                credit_type.movement = True
                credit_type.save()
                # TODO add log
            if not subject_credit:
                subject_credit = CcpCredits()
                subject_credit.subject_id = movement.subject_id
                subject_credit.subject_type = movement.subject_type
                subject_credit.credit_type = credit_type
                subject_credit.save()
                # TODO add log


    return

"""
    function to get a list of movements based on filter criteria
    :param filter: dictionary containing filter criteria
    :return: list of CcpMovement objects
"""
def get_list(filter):
    ids = filter.get('ids', [])

    movements = CcpMovement.objects
    if ids:
        movements = movements.filter(id__in=ids)

    return movements.all()