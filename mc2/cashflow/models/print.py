from django.db import models

class CcpPrintCategory(models.Model):
    name = models.CharField(max_length=255)
    ordering = models.IntegerField()

    class Meta:
        db_table = 'ccp_print_category'


class CcpPrintCategoryMovementType(models.Model):
    ccp_print_category = models.ForeignKey(CcpPrintCategory, models.DO_NOTHING)
    ccp_type = models.ForeignKey('CcpType', models.DO_NOTHING)

    class Meta:
        db_table = 'ccp_print_category_movement_type'
        unique_together = (('ccp_print_category', 'ccp_type'),)
