from django.db import models

class CcpCredits(models.Model):
    subject_id = models.IntegerField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    credit_type = models.ForeignKey('CcpCreditsType', models.DO_NOTHING)

    class Meta:
        db_table = 'ccp_credits'


class CcpCreditsType(models.Model):
    description = models.CharField(max_length=255, blank=True, null=True)
    is_default = models.BooleanField(blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    discount = models.BooleanField()
    dote = models.IntegerField()
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    movement = models.BooleanField()
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile_uscite = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    ccp_payment_method = models.ForeignKey('CcpPaymentMethod', models.DO_NOTHING, db_column='ccp_payment_method', blank=True, null=True)
    show_on_site = models.BooleanField()

    class Meta:
        db_table = 'ccp_credits_type'