from django.db import models

class CcpDeposits(models.Model):
    operation_date = models.BigIntegerField()
    accountable_date = models.BigIntegerField(blank=True, null=True)
    amount = models.FloatField()
    credits = models.ForeignKey('CcpCredits', models.DO_NOTHING)
    payment_method_id = models.IntegerField(blank=True, null=True)
    payer_type = models.CharField(max_length=1, blank=True, null=True)
    payer_id = models.Char<PERSON>ield(max_length=30, blank=True, null=True)
    payer_name = models.Char<PERSON><PERSON>(max_length=50, blank=True, null=True)
    payer_surname = models.Char<PERSON><PERSON>(max_length=50, blank=True, null=True)
    payer_fiscal_code = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=80, blank=True, null=True)
    payer_city = models.Char<PERSON>ield(max_length=30, blank=True, null=True)
    payer_province = models.Char<PERSON>ield(max_length=2, blank=True, null=True)
    payer_zip_code = models.Char<PERSON>ield(max_length=5, blank=True, null=True)
    description = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    deposit_type = models.CharField(max_length=1, blank=True, null=True)
    tx_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_deposits'