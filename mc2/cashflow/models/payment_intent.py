from django.db import models
from .movement import CcpMovement
from .type import CcpType

class CcpPaymentIntents(models.Model):
    token = models.CharField(max_length=255, blank=True, null=True)
    payment_id = models.BigIntegerField(blank=True, null=True)
    payment_object_type = models.CharField(max_length=20, blank=True, null=True)
    payment_object_id = models.BigIntegerField(blank=True, null=True)
    payment_status = models.CharField(max_length=255, blank=True, null=True)
    charge_status = models.CharField(max_length=20, blank=True, null=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    payer_type = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_surname = models.CharField(max_length=255, blank=True, null=True)
    payer_name = models.Char<PERSON><PERSON>(max_length=255, blank=True, null=True)
    payer_fiscal_code = models.CharField(max_length=255, blank=True, null=True)
    payer_address = models.CharField(max_length=255, blank=True, null=True)
    payer_city = models.CharField(max_length=255, blank=True, null=True)
    payer_province = models.CharField(max_length=255, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=255, blank=True, null=True)
    subject_data = models.TextField(blank=True, null=True)
    subject_class = models.CharField(max_length=255, blank=True, null=True)
    subject_school_address_code = models.CharField(max_length=255, blank=True, null=True)
    subject_school_address = models.CharField(max_length=255, blank=True, null=True)
    school_year = models.CharField(max_length=9, blank=True, null=True)
    subject_id = models.BigIntegerField(blank=True, null=True)
    date_created = models.DateTimeField(blank=True, null=True)
    date_succeeded = models.DateTimeField(blank=True, null=True)
    date_canceled = models.DateTimeField(blank=True, null=True)
    date_failed = models.DateTimeField(blank=True, null=True)
    date_charged = models.DateTimeField(blank=True, null=True)
    payment_channel = models.CharField(max_length=255, blank=True, null=True)
    bank_account_id = models.IntegerField()
    date_processed = models.DateTimeField(blank=True, null=True)
    channel_key = models.TextField(blank=True, null=True)
    channel_id = models.IntegerField(null=True, blank=True)
    satispay_private_key = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment_intents'


    def succeeded(self):
        return bool(self.date_succeeded)

    """
    returns the total amount of the transaction
    """
    def transaction_total(self):
        return CcpPaymentIntents.objects.filter(token=self.token).aggregate(
            total=models.Sum('amount')
        )['total']


    """
    returns all the movements related to the transaction
    """

    def movements(self):
        if self.succeeded():
            return CcpMovement.objects.filter(payment_intent_token=self.token).all()

        return []

    def sito_app_movements(self):
        movements = self.movements()
        result = []
        for movement in movements:
            result.append({
                'id': movement.id,
                'tipo': movement.type.name,
                'totale': movement.get_total(),
                'pagato': movement.unsolved() == False,
                'descrizione': movement.description,
                'anno_scolastico': movement.school_year,
                'soggetto': movement.subject_data,
                'classe': movement.subject_class,
                'indirizzo': movement.subject_school_address_code,
                'sconto_applicato': movement.get_discount_total()
            })


        return result


    """
    return items related to the transaction
    """
    def items(self):
        m = []
        t = []
        items = []
        intents = list(CcpPaymentIntents.objects.filter(token=self.token).all())

        for intent in intents:
            if intent.payment_object_type == 'movement':
                m.append(intent.payment_object_id)

            if intent.payment_object_type == 'movement_type':
                t.append(intent.payment_object_id)

        if m:
            movements = CcpMovement.objects.filter(id__in=m)
            for movement in movements:
                items.append({
                    'id': movement.id,
                    'tipo': 'movimento',
                    'totale': movement.get_total(),
                    'qta': 1,
                    'descrizione': movement.description
                })


        if t:
            types = CcpType.objects.filter(id__in=t)
            for type in types:
                items.append({
                    'id': type.id,
                    'tipo': 'tipo_movimento',
                    'totale': type.get_total(),
                    'qta': 1,
                    'descrizione': type.name
                })


        return items