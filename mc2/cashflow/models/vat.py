from django.db import models

class CcpVatCode(models.Model):
    code = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField()
    exemption = models.BooleanField(blank=True, null=True)
    sdi_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'ccp_vat_code'