from django.db import models

class CcpCategory(models.Model):
    name = models.CharField(unique=True, max_length=50)
    initial_balance = models.FloatField(default=0)

    class Meta:
        db_table = 'ccp_category'

    def __str__(self):
        return self.name


class CcpCategoryBanks(models.Model):
    category_id = models.IntegerField(primary_key=True)
    bank_id = models.IntegerField()
    initial_balance = models.FloatField()

    class Meta:
        db_table = 'ccp_category_banks'
        unique_together = (('category_id', 'bank_id'),)