from django.db import models
from .invoice import CcpInvoice
from .easy import CcpEasySelect

class CcpMovement(models.Model):
    type = models.ForeignKey('CcpType', on_delete=models.CASCADE, db_column='type_id')
    miscellaneous = models.TextField(blank=True, null=True)
    number = models.IntegerField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)
    school_year = models.CharField(max_length=255, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    subject_data = models.TextField(blank=True, null=True)
    subject_seat = models.IntegerField(blank=True, null=True)
    subject_class = models.CharField(max_length=64, blank=True, null=True)
    amount = models.FloatField()
    creation_date = models.BigIntegerField()
    subject_type = models.CharField(max_length=1, default='S')
    expiration_date = models.BigIntegerField()
    tmp_number = models.TextField(blank=True, null=True)
    invoice = models.ForeignKey('CcpInvoice', models.DO_NOTHING, blank=True, null=True)
    subject_school_address_code = models.CharField(max_length=255, blank=True, null=True)
    subject_school_address = models.CharField(max_length=255, blank=True, null=True)
    invoice_code = models.CharField(max_length=255, blank=True, null=True)
    description = models.CharField(max_length=255, default='')
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)
    ccp_easy_select = models.ForeignKey('CcpEasySelect', models.DO_NOTHING, db_column='ccp_easy_select', blank=True, null=True)
    subject_school_year = models.CharField(max_length=255, blank=True, null=True)
    payment_intent_token = models.CharField(max_length=255, blank=True, null=True)
    payment_intent_id = models.IntegerField(blank=True, null=True)
    locked = models.BooleanField()
    date_published = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_movement'

    def total_paid(self):
        total_paid = 0
        for p in self.ccppayment_set.all():
            total_paid += p.amount

        return total_paid

    def unsolved(self):
        return self.get_total() - self.total_paid()

    def get_total(self):
        return self.amount + self.get_discount_total()

    def get_discount(self):
        return 0.0


    def get_discount_total(self):
        val = 0
        for movement_add in self.ccpmovementadditional_set.all():
            val += movement_add.get_total()
        return val


    def create(self):


        return self