from django.db import models

class CcpInvoice(models.Model):
    number = models.IntegerField()
    date = models.DateTimeField()
    accountholder = models.TextField()
    rows = models.TextField()
    total = models.FloatField()
    bank = models.TextField()
    expiration_date = models.DateTimeField(blank=True, null=True)
    incoming = models.BooleanField()
    xml_name = models.CharField(max_length=255, blank=True, null=True)
    expiration_text = models.TextField(blank=True, null=True)
    table_text = models.TextField(blank=True, null=True)
    ds_name = models.CharField(max_length=255, blank=True, null=True)
    ds_id = models.CharField(max_length=255, blank=True, null=True)
    credit_note = models.BooleanField(blank=True, null=True)
    payment_method = models.IntegerField(blank=True, null=True)
    publication_path = models.CharField(max_length=255, blank=True, null=True)
    header = models.TextField()
    suffix = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_invoice'


class CcpInvoiceTransmission(models.Model):
    transmission_id = models.CharField(max_length=255)
    ccp_invoice = models.ForeignKey(CcpInvoice, models.DO_NOTHING, db_column='ccp_invoice', blank=True, null=True)
    date = models.DateTimeField()
    status = models.CharField(max_length=2)
    description = models.TextField()

    class Meta:
        db_table = 'ccp_invoice_transmission'