from django.db import models

class CcpPaymentMethod(models.Model):
    name = models.Char<PERSON><PERSON>(unique=True, max_length=50)
    easy_code = models.IntegerField(blank=True, null=True)
    piano_conti = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    easy_by_bank = models.BooleanField(blank=True, null=True)
    easy_export_grouped = models.BooleanField(blank=True, null=True)
    massive_payment_group = models.BooleanField(blank=True, null=True)
    causale_contabile = models.CharField(max_length=50, blank=True, null=True)
    causale_contabile_uscite = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    data_raggruppamento = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    tipo_raggruppamento = models.CharField(max_length=255, blank=True, null=True)
    piano_conti_da_tipo = models.BooleanField(blank=True, null=True)
    piano_conti_uscite_da_tipo = models.<PERSON><PERSON>anField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment_method'
