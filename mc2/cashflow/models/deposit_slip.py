from django.db import models

class CcpDepositSlip(models.Model):
    number = models.IntegerField()
    date = models.DateTimeField()
    bank_account = models.ForeignKey('CoreBankAccount', models.DO_NOTHING, db_column='bank_account', blank=True, null=True)
    bank_account_iban = models.CharField(max_length=255)
    bank_account_name = models.CharField(max_length=255)
    payment_method = models.ForeignKey('CcpPaymentMethod', models.DO_NOTHING, db_column='payment_method', blank=True, null=True)
    payment_method_name = models.CharField(max_length=255)
    bank_account_cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.CharField(max_length=255, blank=True, null=True)
    school_year = models.CharField(max_length=9, blank=True, null=True)

    class Meta:
        db_table = 'ccp_deposit_slip'


class CcpInvoiceDepositSlip(models.Model):
    ccp_invoice = models.ForeignKey('CcpInvoice', models.DO_NOTHING, db_column='ccp_invoice', blank=True, null=True)
    ccp_deposit_slip = models.ForeignKey(CcpDepositSlip, models.DO_NOTHING, db_column='ccp_deposit_slip', blank=True, null=True)
    unpaid_date = models.DateTimeField(blank=True, null=True)
    unpaid_note = models.TextField()
    row_number = models.IntegerField()
    total = models.FloatField()
    collection_cost = models.FloatField()
    bollo = models.FloatField()
    movements_total = models.FloatField()
    iban = models.TextField(blank=True, null=True)
    codice_rid = models.TextField(blank=True, null=True)
    data_mandato_rid = models.TextField(blank=True, null=True)
    first_sepa = models.TextField(blank=True, null=True)
    surname = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    province = models.CharField(max_length=255, blank=True, null=True)
    zip_code = models.CharField(max_length=255, blank=True, null=True)
    fiscal_code = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_invoice_deposit_slip'