from django.db import models

class CcpType(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=255)
    amount = models.FloatField()
    governative = models.BooleanField()
    expiration_date = models.BigIntegerField(blank=True, null=True)
    cumulative = models.IntegerField(default=0)
    school_year = models.CharField(max_length=9)
    category = models.ForeignKey('CcpCategory', models.DO_NOTHING)
    incoming = models.BooleanField()
    section = models.CharField(max_length=255, blank=True, null=True)
    online_payment = models.BooleanField()
    invoice_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=255, blank=True, null=True)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)
    include_vat = models.BooleanField()
    vat = models.FloatField(blank=True, null=True)
    bollo = models.<PERSON><PERSON>anField()
    vat_code_id = models.IntegerField(blank=True, null=True)
    easy_description = models.CharField(max_length=20, blank=True, null=True)
    payment_mail = models.CharField(max_length=255, blank=True, null=True)
    online_payment_status = models.IntegerField(blank=True, null=True)
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    ccp_credits_type = models.IntegerField(blank=True, null=True)
    centro_costo_ricavo = models.CharField(max_length=50, blank=True, null=True)
    id_importazione = models.CharField(max_length=50, blank=True, null=True)
    ccp_ae_category = models.ForeignKey('CcpAeCategory', models.DO_NOTHING, blank=True, null=True)
    pubblica_pagati_online = models.BooleanField(blank=True, null=True)
    include_bollo = models.BooleanField(blank=True, null=True)
    easy_code_contropartita = models.CharField(max_length=255, blank=True, null=True)
    short_desc = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        db_table = 'ccp_type'

    def __str__(self):
        return f"{self.name} - {self.school_year} - {self.category}"

    def get_total(self):
        if self.ccptypestep_set.count() > 0:
            return self.ccptypestep_set.aggregate(total=models.Sum('value'))['total']
        else:
            return 0.0

    # assolutamente da rivedere
    def get_amount(self):
        pass


class CcpTypeStep(models.Model):
    ccp_type = models.ForeignKey(CcpType, models.DO_NOTHING, db_column='ccp_type')
    expiration = models.CharField(max_length=255, blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    description = models.CharField(max_length=255)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_type_step'