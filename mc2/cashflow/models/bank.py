from django.db import models

class CoreBankAccount(models.Model):
    country_code = models.Char<PERSON>ield(max_length=2, blank=True, null=True)
    check_code = models.CharField(max_length=2, blank=True, null=True)
    bban = models.CharField(max_length=100)
    denomination = models.CharField(max_length=100)
    initial_balance = models.FloatField()
    type = models.CharField(max_length=1)
    ise_id = models.IntegerField()
    ise_type = models.CharField(max_length=1)
    invoice_default = models.BooleanField(blank=True, null=True)
    online_payment_default = models.BooleanField()
    cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    pvr_number = models.CharField(max_length=20)
    agency_name = models.CharField(max_length=50, blank=True, null=True)
    agency_city = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    agency_zip_code = models.CharField(max_length=10, blank=True, null=True)
    qr_iban = models.Char<PERSON>ield(max_length=27, blank=True, null=True)
    qr_bank_account = models.CharField(max_length=27, blank=True, null=True)
    collection_cost = models.FloatField()
    sepa_regex = models.CharField(max_length=255, blank=True, null=True)
    sepa_accent = models.BooleanField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    customer_desc = models.CharField(max_length=255)

    class Meta:
        db_table = 'core_bank_account'