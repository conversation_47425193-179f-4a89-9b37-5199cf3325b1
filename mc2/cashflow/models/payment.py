from django.db import models

class CcpPayment(models.Model):
    movement = models.ForeignKey('CcpMovement', on_delete=models.CASCADE, db_column='movement_id')
    operation_date = models.BigIntegerField()
    accountable_date = models.BigIntegerField()
    amount = models.FloatField()
    payment_method = models.ForeignKey('CcpPaymentMethod', on_delete=models.DO_NOTHING, db_column='payment_method_id')
    bollettino = models.CharField(max_length=50, blank=True, null=True)
    account = models.ForeignKey('CoreBankAccount', models.DO_NOTHING)
    account_reference = models.CharField(max_length=50, blank=True, null=True)
    payer_type = models.CharField(max_length=1)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_name = models.Char<PERSON><PERSON>(max_length=50)
    payer_surname = models.Cha<PERSON><PERSON><PERSON>(max_length=50)
    payer_fiscal_code = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=255, blank=True, null=True)
    payer_city = models.CharField(max_length=255, blank=True, null=True)
    payer_province = models.CharField(max_length=2, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=5, blank=True, null=True)
    receipt = models.ForeignKey('CcpReceipt', models.DO_NOTHING, blank=True, null=True)
    ccp_credit = models.IntegerField(blank=True, null=True)
    ccp_deposit_slip = models.IntegerField(blank=True, null=True)
    easy_import_protocol = models.IntegerField(blank=True, null=True)
    covered_movement = models.ForeignKey('CcpMovement', models.DO_NOTHING, blank=True, null=True, related_name='covered_movement', db_column='covered_movement_id')
    payment_group = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment'

    def get_piano_conti(self):
        if self.payment_method.easy_by_bank:
            return self.account.piano_conti
        return self.payment_method.piano_conti