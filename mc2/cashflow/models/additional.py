from django.db import models


class CcpAdditional(models.Model):
    name = models.Char<PERSON><PERSON>(unique=True, max_length=255)
    positive = models.BooleanField()
    percentual = models.BooleanField()
    payment = models.BooleanField()
    amount = models.FloatField(blank=True, null=True)
    code = models.CharField(max_length=20, blank=True, null=True)
    on_gross = models.BooleanField()
    codice_conto = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_additional'




class CcpAdditionalTemplates(models.Model):
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING, blank=True, null=True)
    type = models.ForeignKey('CcpType', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        db_table = 'ccp_additional_templates'


class CcpMovementAdditional(models.Model):
    movement = models.ForeignKey('CcpMovement', models.DO_NOTHING)
    additional = models.Foreign<PERSON>ey(CcpAdditional, models.DO_NOTHING)
    amount = models.Float<PERSON>ield()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    abs_amount = models.FloatField()

    class Meta:
        db_table = 'ccp_movement_additional'

    def get_total(self):
        return self.abs_amount

class CcpPaymentAdditional(models.Model):
    payment = models.OneToOneField('CcpPayment', models.DO_NOTHING, primary_key=True)
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING)
    amount = models.FloatField()

    class Meta:
        db_table = 'ccp_payment_additional'
        unique_together = (('payment', 'additional'),)


class CcpTypeAdditional(models.Model):
    type = models.ForeignKey('CcpType', models.DO_NOTHING)
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING)
    amount = models.FloatField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    discount_order = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_type_additional'