from django.db import models

class CcpReminder(models.Model):
    creation = models.DateTimeField()
    reminder_type = models.Char<PERSON>ield(max_length=255)
    sent = models.DateTimeField(blank=True, null=True)
    mail = models.CharField(max_length=255)
    message = models.TextField()
    tried = models.BooleanField()
    confirmed = models.DateTimeField(blank=True, null=True)
    student_text = models.CharField(max_length=255, blank=True, null=True)
    accountholder_text = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_reminder'


class CcpReminderSubjects(models.Model):
    ccp_reminder = models.ForeignKey(CcpReminder, models.DO_NOTHING)
    subject_id = models.IntegerField()

    class Meta:
        db_table = 'ccp_reminder_subjects'