from datetime import datetime

from django.contrib import admin
from django.forms import DateField
from django.contrib.admin.widgets import AdminDateWidget
from django.forms import ModelForm as BaseModelForm

from ..models import CcpMovement as Movement
from ..models import <PERSON>cpType as MovementType
from ..models import CcpCategory as Category
from ..models import CcpPaymentIntents
from .forms import MovementForm

@admin.register(Movement)
class CcpMovementAdmin(admin.ModelAdmin):
    form = MovementForm
    list_display = (
        'expiration_date_fn',
        'type',
        'subject_data',
        'subject_school_year',
        'invoice',
        'amount'
    )
    raw_id_fields = ('type', )
    fieldsets = [
        (
            None,
            {'fields': [
                ('type', 'amount'),
                ('creation_date',  'expiration_date'),
                ('subject_school_year', 'school_year',),
                ('subject_id',  'subject_type', 'subject_data'),
                'invoice',
                'note',
            ]}
        ),
        (
            'Extra',
            {
                'fields': [
                'payment_intent_token',
                'payment_intent_id',
                'date_published',
                'locked',
            ]}
        )
    ]

    def expiration_date_fn(self, obj):
        return datetime.fromtimestamp(obj.expiration_date).date()


@admin.register(MovementType)
class MovementTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'category', 'school_year', 'incoming', 'ccp_ae_category')


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name')


@admin.register(CcpPaymentIntents)
class PaymentIntentAdmin(admin.ModelAdmin):
    list_display = ('id', 'amount', 'payment_channel', 'token', 'date_created', 'date_processed', 'date_succeeded')
