
from datetime import datetime, time
from django.forms import DateField
from django.contrib.admin.widgets import AdminDateWidget
from django.forms import ModelForm

from ..models import CcpMovement as Movement


class MovementForm(ModelForm):
    expiration_date = DateField(widget=AdminDateWidget)
    creation_date = DateField(widget=AdminDateWidget)

    class Meta:
       model = Movement
       fields = '__all__'

    def get_timestamp_from_date(self, date):
        if date:
            return int(datetime.combine(date, time(0,0,0)).timestamp())
        return None

    def clean_expiration_date(self):
        return self.get_timestamp_from_date(self.cleaned_data['expiration_date'])

    def clean_creation_date(self):
        return self.get_timestamp_from_date(self.cleaned_data['creation_date'])

    def __init__(self,  *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance:
            if self.instance.expiration_date:
                self.initial['expiration_date'] = datetime.fromtimestamp(self.instance.expiration_date).date()

            if self.instance.creation_date:
                self.initial['creation_date'] = datetime.fromtimestamp(self.instance.creation_date).date()
