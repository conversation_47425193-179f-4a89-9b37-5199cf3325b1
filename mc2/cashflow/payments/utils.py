from mc2.cashflow.models.payment import CcpPayment
from mc2.common.utils import format_name

def create(data, *args, **kwargs):
    p = CcpPayment()
    p.payer_type = data.get('payer_type', None)
    p.payer_name = format_name(data.get('payer_name', None))
    p.payer_surname = format_name(data.get('payer_surname', None))
    p.payer_fiscal_code = data.get('payer_fiscal_code', None)
    p.payer_address = format_name(data.get('payer_address', None))
    p.payer_city = format_name(data.get('payer_city', None))
    p.payer_province = data.get('payer_province', None)
    p.payer_zip_code = data.get('payer_zip_code', None)
    p.movement = data.get('movement', None)
    p.amount = data.get('amount', None)
    p.account = data.get('account', None)
    p.payment_method = data.get('payment_method', None)
    p.operation_date = data.get('operation_date', None)
    p.accountable_date = data.get('accountable_date', None)
    p.account_reference = data.get('account_reference', None)
    p.ccp_credit = data.get('ccp_credit', None)
    p.save()
    return p

"""
    function to get a list of payments based on filter criteria
    :param filter: dictionary containing filter criteria
    :return: list of CcpPayment objects
"""
def get_list(filter):
    ids = filter.get('ids', [])

    payments = CcpPayment.objects
    if ids:
        payments = payments.filter(id__in=ids)

    return payments.all()