from django.db import models


class CcpAdditional(models.Model):
    name = models.Char<PERSON>ield(unique=True, max_length=255)
    positive = models.BooleanField()
    percentual = models.BooleanField()
    payment = models.BooleanField()
    amount = models.FloatField(blank=True, null=True)
    code = models.CharField(max_length=20, blank=True, null=True)
    on_gross = models.BooleanField()
    codice_conto = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_additional'


class CcpAdditionalTemplates(models.Model):
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING, blank=True, null=True)
    type = models.ForeignKey('CcpType', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        db_table = 'ccp_additional_templates'


class CcpAeCategory(models.Model):
    description = models.TextField()
    row = models.IntegerField()
    incoming = models.BooleanField()

    class Meta:
        db_table = 'ccp_ae_category'

    def __str__(self):
        return self.description


class CcpCategory(models.Model):
    name = models.Char<PERSON>ield(unique=True, max_length=50)
    initial_balance = models.FloatField(default=0)

    class Meta:
        db_table = 'ccp_category'

    def __str__(self):
        return self.name


class CcpCategoryBanks(models.Model):
    category_id = models.IntegerField(primary_key=True)
    bank_id = models.IntegerField()
    initial_balance = models.FloatField()

    class Meta:
        db_table = 'ccp_category_banks'
        unique_together = (('category_id', 'bank_id'),)


class CcpCredits(models.Model):
    subject_id = models.IntegerField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    credit_type = models.ForeignKey('CcpCreditsType', models.DO_NOTHING)

    class Meta:
        db_table = 'ccp_credits'


class CcpCreditsType(models.Model):
    description = models.CharField(max_length=255, blank=True, null=True)
    is_default = models.BooleanField(blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    discount = models.BooleanField()
    dote = models.IntegerField()
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    movement = models.BooleanField()
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile_uscite = models.CharField(max_length=255, blank=True, null=True)
    ccp_payment_method = models.ForeignKey('CcpPaymentMethod', models.DO_NOTHING, db_column='ccp_payment_method', blank=True, null=True)
    show_on_site = models.BooleanField()

    class Meta:
        db_table = 'ccp_credits_type'


class CcpDepositSlip(models.Model):
    number = models.IntegerField()
    date = models.DateTimeField()
    bank_account = models.ForeignKey('CoreBankAccount', models.DO_NOTHING, db_column='bank_account', blank=True, null=True)
    bank_account_iban = models.CharField(max_length=255)
    bank_account_name = models.CharField(max_length=255)
    payment_method = models.ForeignKey('CcpPaymentMethod', models.DO_NOTHING, db_column='payment_method', blank=True, null=True)
    payment_method_name = models.CharField(max_length=255)
    bank_account_cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.CharField(max_length=255, blank=True, null=True)
    school_year = models.CharField(max_length=9, blank=True, null=True)

    class Meta:
        db_table = 'ccp_deposit_slip'


class CcpDeposits(models.Model):
    operation_date = models.BigIntegerField()
    accountable_date = models.BigIntegerField(blank=True, null=True)
    amount = models.FloatField()
    credits = models.ForeignKey(CcpCredits, models.DO_NOTHING)
    payment_method_id = models.IntegerField(blank=True, null=True)
    payer_type = models.CharField(max_length=1, blank=True, null=True)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_name = models.CharField(max_length=50, blank=True, null=True)
    payer_surname = models.CharField(max_length=50, blank=True, null=True)
    payer_fiscal_code = models.CharField(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=80, blank=True, null=True)
    payer_city = models.CharField(max_length=30, blank=True, null=True)
    payer_province = models.CharField(max_length=2, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=5, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    deposit_type = models.CharField(max_length=1, blank=True, null=True)
    tx_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_deposits'


class CcpEasySelect(models.Model):
    payment_method = models.IntegerField(blank=True, null=True)
    force_single_invoice = models.BooleanField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_easy_select'


class CcpInvoice(models.Model):
    number = models.IntegerField()
    date = models.DateTimeField()
    accountholder = models.TextField()
    rows = models.TextField()
    total = models.FloatField()
    bank = models.TextField()
    expiration_date = models.DateTimeField(blank=True, null=True)
    incoming = models.BooleanField()
    xml_name = models.CharField(max_length=255, blank=True, null=True)
    expiration_text = models.TextField(blank=True, null=True)
    table_text = models.TextField(blank=True, null=True)
    ds_name = models.CharField(max_length=255, blank=True, null=True)
    ds_id = models.CharField(max_length=255, blank=True, null=True)
    credit_note = models.BooleanField(blank=True, null=True)
    payment_method = models.IntegerField(blank=True, null=True)
    publication_path = models.CharField(max_length=255, blank=True, null=True)
    header = models.TextField()
    suffix = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_invoice'


class CcpInvoiceDepositSlip(models.Model):
    ccp_invoice = models.ForeignKey(CcpInvoice, models.DO_NOTHING, db_column='ccp_invoice', blank=True, null=True)
    ccp_deposit_slip = models.ForeignKey(CcpDepositSlip, models.DO_NOTHING, db_column='ccp_deposit_slip', blank=True, null=True)
    unpaid_date = models.DateTimeField(blank=True, null=True)
    unpaid_note = models.TextField()
    row_number = models.IntegerField()
    total = models.FloatField()
    collection_cost = models.FloatField()
    bollo = models.FloatField()
    movements_total = models.FloatField()
    iban = models.TextField(blank=True, null=True)
    codice_rid = models.TextField(blank=True, null=True)
    data_mandato_rid = models.TextField(blank=True, null=True)
    first_sepa = models.TextField(blank=True, null=True)
    surname = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    province = models.CharField(max_length=255, blank=True, null=True)
    zip_code = models.CharField(max_length=255, blank=True, null=True)
    fiscal_code = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_invoice_deposit_slip'


class CcpInvoiceTransmission(models.Model):
    transmission_id = models.CharField(max_length=255)
    ccp_invoice = models.ForeignKey(CcpInvoice, models.DO_NOTHING, db_column='ccp_invoice', blank=True, null=True)
    date = models.DateTimeField()
    status = models.CharField(max_length=2)
    description = models.TextField()

    class Meta:
        db_table = 'ccp_invoice_transmission'







class CcpMovementAdditional(models.Model):
    movement = models.ForeignKey(CcpMovement, models.DO_NOTHING)
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING)
    amount = models.FloatField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    abs_amount = models.FloatField()

    class Meta:
        db_table = 'ccp_movement_additional'


class CcpPayment(models.Model):
    movement = models.ForeignKey(CcpMovement, models.DO_NOTHING)
    operation_date = models.BigIntegerField()
    accountable_date = models.BigIntegerField()
    amount = models.FloatField()
    payment_method = models.ForeignKey('CcpPaymentMethod', models.DO_NOTHING)
    bollettino = models.CharField(max_length=50, blank=True, null=True)
    account = models.ForeignKey('CoreBankAccount', models.DO_NOTHING)
    account_reference = models.CharField(max_length=50, blank=True, null=True)
    payer_type = models.CharField(max_length=1)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_name = models.CharField(max_length=50)
    payer_surname = models.CharField(max_length=50)
    payer_fiscal_code = models.CharField(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=255, blank=True, null=True)
    payer_city = models.CharField(max_length=255, blank=True, null=True)
    payer_province = models.CharField(max_length=2, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=5, blank=True, null=True)
    receipt = models.ForeignKey('CcpReceipt', models.DO_NOTHING, blank=True, null=True)
    ccp_credit = models.IntegerField(blank=True, null=True)
    ccp_deposit_slip = models.IntegerField(blank=True, null=True)
    easy_import_protocol = models.IntegerField(blank=True, null=True)
    covered_movement = models.ForeignKey(CcpMovement, models.DO_NOTHING, blank=True, null=True, related_name='covered_movement')
    payment_group = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment'


class CcpPaymentAdditional(models.Model):
    payment = models.OneToOneField(CcpPayment, models.DO_NOTHING, primary_key=True)
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING)
    amount = models.FloatField()

    class Meta:
        db_table = 'ccp_payment_additional'
        unique_together = (('payment', 'additional'),)


class CcpPaymentIntents(models.Model):
    token = models.CharField(max_length=255, blank=True, null=True)
    payment_id = models.BigIntegerField(blank=True, null=True)
    payment_object_type = models.CharField(max_length=20, blank=True, null=True)
    payment_object_id = models.BigIntegerField(blank=True, null=True)
    payment_status = models.CharField(max_length=255, blank=True, null=True)
    charge_status = models.CharField(max_length=20, blank=True, null=True)
    amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    payer_type = models.CharField(max_length=20, blank=True, null=True)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    school_year = models.CharField(max_length=9, blank=True, null=True)
    subject_id = models.BigIntegerField(blank=True, null=True)
    date_created = models.DateTimeField(blank=True, null=True)
    date_succeeded = models.DateTimeField(blank=True, null=True)
    date_canceled = models.DateTimeField(blank=True, null=True)
    date_failed = models.DateTimeField(blank=True, null=True)
    date_charged = models.DateTimeField(blank=True, null=True)
    payment_channel = models.CharField(max_length=255, blank=True, null=True)
    bank_account_id = models.IntegerField()
    date_processed = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment_intents'


class CcpPaymentMethod(models.Model):
    name = models.CharField(unique=True, max_length=50)
    easy_code = models.IntegerField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    easy_by_bank = models.BooleanField(blank=True, null=True)
    easy_export_grouped = models.BooleanField(blank=True, null=True)
    massive_payment_group = models.BooleanField(blank=True, null=True)
    causale_contabile = models.CharField(max_length=50, blank=True, null=True)
    causale_contabile_uscite = models.CharField(max_length=50, blank=True, null=True)
    data_raggruppamento = models.CharField(max_length=255, blank=True, null=True)
    tipo_raggruppamento = models.CharField(max_length=255, blank=True, null=True)
    piano_conti_da_tipo = models.BooleanField(blank=True, null=True)
    piano_conti_uscite_da_tipo = models.BooleanField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_payment_method'


class CcpPrintCategory(models.Model):
    name = models.CharField(max_length=255)
    ordering = models.IntegerField()

    class Meta:
        db_table = 'ccp_print_category'


class CcpPrintCategoryMovementType(models.Model):
    ccp_print_category = models.ForeignKey(CcpPrintCategory, models.DO_NOTHING)
    ccp_type = models.ForeignKey('CcpType', models.DO_NOTHING)

    class Meta:
        db_table = 'ccp_print_category_movement_type'
        unique_together = (('ccp_print_category', 'ccp_type'),)


class CcpReceipt(models.Model):
    number = models.IntegerField()
    date = models.BigIntegerField()
    receipt = models.BooleanField()
    publication_path = models.CharField(max_length=255, blank=True, null=True)
    groupment = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_receipt'


class CcpReminder(models.Model):
    creation = models.DateTimeField()
    reminder_type = models.CharField(max_length=255)
    sent = models.DateTimeField(blank=True, null=True)
    mail = models.CharField(max_length=255)
    message = models.TextField()
    tried = models.BooleanField()
    confirmed = models.DateTimeField(blank=True, null=True)
    student_text = models.CharField(max_length=255, blank=True, null=True)
    accountholder_text = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_reminder'


class CcpReminderSubjects(models.Model):
    ccp_reminder = models.ForeignKey(CcpReminder, models.DO_NOTHING)
    subject_id = models.IntegerField()

    class Meta:
        db_table = 'ccp_reminder_subjects'


class CcpType(models.Model):
    name = models.CharField(max_length=255)
    amount = models.FloatField()
    governative = models.BooleanField()
    expiration_date = models.BigIntegerField(blank=True, null=True)
    cumulative = models.IntegerField(default=0)
    school_year = models.CharField(max_length=9)
    category = models.ForeignKey(CcpCategory, models.DO_NOTHING)
    incoming = models.BooleanField()
    section = models.CharField(max_length=255, blank=True, null=True)
    online_payment = models.BooleanField()
    invoice_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=255, blank=True, null=True)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)
    include_vat = models.BooleanField()
    vat = models.FloatField(blank=True, null=True)
    bollo = models.BooleanField()
    vat_code_id = models.IntegerField(blank=True, null=True)
    easy_description = models.CharField(max_length=20, blank=True, null=True)
    payment_mail = models.CharField(max_length=255, blank=True, null=True)
    online_payment_status = models.IntegerField(blank=True, null=True)
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    ccp_credits_type = models.IntegerField(blank=True, null=True)
    centro_costo_ricavo = models.CharField(max_length=50, blank=True, null=True)
    id_importazione = models.CharField(max_length=50, blank=True, null=True)
    ccp_ae_category = models.ForeignKey(CcpAeCategory, models.DO_NOTHING, blank=True, null=True)
    pubblica_pagati_online = models.BooleanField(blank=True, null=True)
    include_bollo = models.BooleanField(blank=True, null=True)
    easy_code_contropartita = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'ccp_type'

    def __str__(self):
        return f"{self.name} ({self.school_year})"

    def get_total(self):
        if self.ccptypestep_set.count() > 0:
            return self.ccptypestep_set.aggregate(total=models.Sum('value'))['total']
        else:
            return 0.0


class CcpTypeAdditional(models.Model):
    type = models.ForeignKey(CcpType, models.DO_NOTHING)
    additional = models.ForeignKey(CcpAdditional, models.DO_NOTHING)
    amount = models.FloatField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    discount_order = models.IntegerField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_type_additional'


class CcpTypeStep(models.Model):
    ccp_type = models.ForeignKey(CcpType, models.DO_NOTHING, db_column='ccp_type')
    expiration = models.CharField(max_length=255, blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    description = models.CharField(max_length=255)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'ccp_type_step'


class CcpVatCode(models.Model):
    code = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField()
    exemption = models.BooleanField(blank=True, null=True)
    sdi_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'ccp_vat_code'


class CoreBankAccount(models.Model):
    country_code = models.CharField(max_length=2, blank=True, null=True)
    check_code = models.CharField(max_length=2, blank=True, null=True)
    bban = models.CharField(max_length=100)
    denomination = models.CharField(max_length=100)
    initial_balance = models.FloatField()
    type = models.CharField(max_length=1)
    ise_id = models.IntegerField()
    ise_type = models.CharField(max_length=1)
    invoice_default = models.BooleanField(blank=True, null=True)
    online_payment_default = models.BooleanField()
    cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.CharField(max_length=255, blank=True, null=True)
    pvr_number = models.CharField(max_length=20)
    agency_name = models.CharField(max_length=50, blank=True, null=True)
    agency_city = models.CharField(max_length=50, blank=True, null=True)
    agency_zip_code = models.CharField(max_length=10, blank=True, null=True)
    qr_iban = models.CharField(max_length=27, blank=True, null=True)
    qr_bank_account = models.CharField(max_length=27, blank=True, null=True)
    collection_cost = models.FloatField()
    sepa_regex = models.CharField(max_length=255, blank=True, null=True)
    sepa_accent = models.BooleanField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    customer_desc = models.CharField(max_length=255)

    class Meta:
        db_table = 'core_bank_account'

