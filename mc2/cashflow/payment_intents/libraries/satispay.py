
import json
import hashlib
import base64
import pytz
import requests
import cryptography
from OpenSSL import crypto
from datetime import datetime, timedelta


class Satispay:

    STAGING_URL = 'https://staging.authservices.satispay.com'
    PRODUCTION_URL = 'https://authservices.satispay.com'
    CALLBACK_URL = 'https://mp.registroelettronico.com/v3/satispay_callback?payment_id={uuid}'
    STAGING_CALLBACK_URL = 'https://mp-dev.registroelettronico.com/v3/satispay_callback?payment_id={uuid}'
    ENV_STAGING = 'STAGING'
    ENV_PRODUCTION = 'PRODUCTION'

    AUTH_HEADER_ALGO = 'rsa-sha256'

    CREATE_PAYMENT_PATH = 'g_business/v1/payments'
    PAYMENT_DETAILS_PATH = 'g_business/v1/payments/{}'
    TEST_SIGNATURE_PATH = 'wally-services/protocol/tests/signature'

    env = None
    private_key = None
    public_key = None
    key_id = None
    now = None
    duration = 125  #Payment duration in seconds
    timeout = 20 #Timeout in seconds

    def __init__(self, env=None, now=None):
        self.set_env(env)
        self.set_now(now or datetime.now())


    def set_private_key(self, val):
        self.private_key = val.replace('\\n', '\n')


    def set_key_id(self, val):
        self.key_id = val


    def set_env(self, val):
        if val == self.ENV_STAGING or val == self.ENV_PRODUCTION:
            self.env = val


    def set_now(self, val):
        self.now = val

    def set_callback_url(self, val):
        self.CALLBACK_URL = val

    def get_body(self, val):
        return json.dumps(val)


    def make_path(self, val):
        if self.env == self.ENV_STAGING:
            return '{}/{}'.format(self.STAGING_URL, val)

        if self.env == self.ENV_PRODUCTION:
            return '{}/{}'.format(self.PRODUCTION_URL, val)

    # fornisce l'host autenticato della richiesta
    def get_auth_host(self):
        if self.env == self.ENV_STAGING:
            return self.STAGING_URL.replace('https://', '')

        if self.env == self.ENV_PRODUCTION:
            return self.PRODUCTION_URL.replace('https://', '')


    def get_callback_url(self):
        if (self.env == self.ENV_STAGING):
            return self.STAGING_CALLBACK_URL

        return self.CALLBACK_URL

    # imposta la durata del pagamento
    def get_expiration_date(self):
        if not self.duration:
            return None


        return (self.now + timedelta(minutes=self.duration)).strftime('%Y-%m-%dT%H:%M:%S.000Z')
            # yyyy-MM-dd'T'HH:mm:ss.SSSZ

    # crea il digest della richiesta
    def make_digest(self, body = None):
        string = ''
        if body:
            string = json.dumps(body)
        return base64.b64encode(hashlib.sha256(string.encode('ascii')).digest()).decode('ascii')


    # trasforma la data nel formato richiesto
    def make_date(self, date):
        try:
            return pytz.timezone('Europe/Rome').localize(date).strftime('%a, %d %b %Y %H:%M:%S %z')
        except:
            return date.strftime('%a, %d %b %Y %H:%M:%S %z')


    # crea la firma della richiesta
    def make_signature(self, **kwargs):
        method = kwargs.get('method', 'GET')
        path = kwargs.get('path', None)
        body = kwargs.get('body', None)
        digest = kwargs.get('digest', None)
        date = kwargs.get('date', None)

        # crea la stringa da firmare
        signature = '(request-target): {} /{}\n'.format(method.lower(), path.lower())
        signature += 'host: {}\n'.format(self.get_auth_host())
        if (body):
            signature += 'content-type: application/json\n'
            signature += 'content-length: {}\n'.format(len(body))

        signature += 'date: {}\n'.format(date)
        signature += 'digest: SHA-256={}'.format(digest)

        # configura la chiave privata
        private_key = cryptography.hazmat.primitives.serialization.load_pem_private_key(
            self.private_key.encode(),
            password=None,
        )

        # firma il contenuto
        sign_refactored= private_key.sign(
            signature.encode(),
            cryptography.hazmat.primitives.asymmetric.padding.PKCS1v15(),
            cryptography.hazmat.primitives.hashes.SHA256()
        )

        # # configura la chiave privata
        # pkey = crypto.load_privatekey(crypto.FILETYPE_PEM, self.private_key)

        # # firma il contenuto
        # sign_old = crypto.sign(pkey, signature, 'sha256')

        #decodifica ascii della firma
        b64signature = base64.b64encode(sign_refactored).decode('ascii')

        return b64signature

    # crea la stringa da inviare tramite header Authorization
    def make_auth_header(self, signature, body=None):
        signature_headers = '(request-target) host date digest'
        if body:
            signature_headers = '(request-target) host content-type content-length date digest'

        auth_header = 'Signature keyId="{}", algorithm="{}", headers="{}", signature="{}"'.format(
            self.key_id,
            self.AUTH_HEADER_ALGO,
            signature_headers,
            signature
        )

        return auth_header

    # test della validità della firma
    def test_signature(self, total):
        dict_body = dict()
        dict_body['flow'] = 'MATCH_CODE'
        dict_body['amount_unit'] = total
        dict_body['currency'] = 'EUR'

        path = self.TEST_SIGNATURE_PATH
        digest = self.make_digest(dict_body)
        date = self.make_date(self.now)

        signature = self.make_signature(
            digest=digest,
            date=date,
            path=path,
            method='POST'
        )


        headers = dict()
        headers['Content-Type'] = 'application/json'
        headers['content-length'] = '{}'.format(len(self.get_body(dict_body)))
        headers['host'] = self.get_auth_host()
        headers['digest'] = 'SHA-256={}'.format(digest)
        headers['date'] = date
        headers['Authorization'] = self.make_auth_header(signature)

        response = requests.post(
            self.make_path(path),
            json=dict_body,
            headers=headers,
            verify=True
        )

        return response.text

    # Ottiene il payment id
    def create_payment(self, total, *args, **kwargs):
        dict_body = dict()
        dict_body['flow'] = 'MATCH_CODE'
        dict_body['amount_unit'] = total
        dict_body['currency'] = 'EUR'
        dict_body['callback_url'] = self.get_callback_url()

        metadata = kwargs.get('metadata', None)
        if metadata:
            dict_body['metadata'] = metadata
        expiration = self.get_expiration_date()
        if expiration:
            dict_body['expiration_date'] = expiration

        path = self.CREATE_PAYMENT_PATH
        digest = self.make_digest(dict_body)
        date = self.make_date(self.now)


        signature = self.make_signature(
            digest=digest,
            date=date,
            path=path,
            method='POST',
            body=self.get_body(dict_body)
        )


        headers = dict()
        headers['Content-Type'] = 'application/json'
        headers['content-length'] = '{}'.format(len(self.get_body(dict_body)))
        headers['host'] = self.get_auth_host()
        headers['digest'] = 'SHA-256={}'.format(digest)
        headers['date'] = date
        headers['Authorization'] = self.make_auth_header(signature, self.get_body(dict_body))

        response = requests.post(
            self.make_path(path),
            json=dict_body,
            headers=headers,
            verify=True,
            timeout=self.timeout
        )

        return response.json()


    def get_payment_details(self, payment_id):

        path = self.PAYMENT_DETAILS_PATH.format(payment_id)
        digest = self.make_digest(None)
        date = self.make_date(self.now)

        signature = self.make_signature(
            digest=digest,
            date=date,
            path=path,
            method='GET'
        )

        headers = dict()
        headers['host'] = self.get_auth_host()
        headers['digest'] = 'SHA-256={}'.format(digest)
        headers['date'] = date
        headers['Authorization'] = self.make_auth_header(signature)


        response = requests.get(
            self.make_path(path),
            headers=headers,
            verify=True,
            timeout=self.timeout
        )
        return response.json()