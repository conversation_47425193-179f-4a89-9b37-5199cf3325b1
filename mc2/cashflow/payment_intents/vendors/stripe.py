import stripe
import json

class StripeVendor():
    public_key = None
    secret_key = None
    publish = None
    # stripe configuration

    # currency
    currency = 'eur'

    # allowed payment methods
    payment_method_types = ['card']

    # max length for metadata items
    meta_items_max_length = 500
    payment_method_id = 10
    transaction_data = None
    transaction_id = None
    lock = True
    def __init__(self, *args, **kwargs):
        self.secret_key = kwargs.get('secret_key', None)
        self.public_key = kwargs.get('public_key', None)
        self.publish = kwargs.get('publish', None)
        self.lock = kwargs.get('lock', True)

    # Formats amount according to stripe requirements
    def get_total_amount(self, amount):
        return int(amount * 100)

    # issues a token request for the payment intent
    def get_token(self, amount, intents):

        stripe.api_key = self.secret_key
        payer_type=intents[0].payer_type
        payer_id=intents[0].payer_id
        subject_id = intents[0].subject_id
        items = []
        for intent in intents:
            type = 'T'
            if intent.payment_object_type == 'movement':
                type = 'M'

            if intent.payment_object_type == 'invoice':
                type = 'I'
            item = dict(
                type=type,
                id=intent.payment_object_id,
                amount=intent.amount
            )
            items.append(item)

        items = json.dumps(items)
        items = items if len(items) < self.meta_items_max_length else 'item payload too long'

        try:
            payment_intent = stripe.PaymentIntent.create(
                amount = self.get_total_amount(amount),
                currency=self.currency,
                metadata=dict(
                    payer_type=payer_type,
                    payer_id=payer_id,
                    subject_id=subject_id,
                    items=items,
                )
            )
        except Exception as e:
            return False

        return payment_intent


    def get_publishable_key(self):
        return self.public_key


    def get_channel_intents_data(self):
        return []

    def handle_webhook(self, request):
        event = None
        payload = request.data
        # endpoint_secret = 'whsec_f1cb69c8f2d30be49e632a7e547694708b57e93431f2506ec60cf94600c47c32'
        try:
            sig_header = request.META['HTTP_STRIPE_SIGNATURE']
            event = stripe.Event.construct_from(payload, stripe.api_key)

        except ValueError as e:
            return False

        if event.type == 'payment_intent.succeeded':
            self.transaction_data = event.data.object
            self.transaction_id = self.transaction_data.id
            return 'succeeded'
            #handle_payment_succeeded(data.id, payment_method_id = self.payment_method_id)

        pass