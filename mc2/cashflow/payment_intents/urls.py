from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import PaymentIntentViewSet
from .webhook import StripeReceiverView
from .webhook import SatispayReceiverView

router = DefaultRouter()
router.register(r'payment_intents', PaymentIntentViewSet, basename='payment_intent')

urlpatterns = [
    path('', include(router.urls), name='payment_intent'),
    path('stripe-payment-receiver/', StripeReceiverView.as_view(), name='stripe-receiver'),
    path('satispay-payment-receiver/', SatispayReceiverView.as_view(), name='satispay-receiver'),
]