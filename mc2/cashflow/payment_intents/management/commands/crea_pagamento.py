import json
from django.core.management.base import BaseCommand, CommandError
from mc2.cashflow.payment_intents.utils import crea_payment_intents, get_totale_intents, init_vendor, init_bank_json
from django.db.models import Sum
import logging


class Command(BaseCommand):

    help = "Crea un record nel database usando i dati del payload JSON."

    def add_arguments(self, parser):
        parser.add_argument('payload', type=str, help='Il JSON contenente i dati del record')

    def handle(self, *args, **kwargs):
        payload = kwargs['payload']

        json_data = json.loads(payload)
        anno_scolastico = json_data.get('anno_scolastico', None)
        utente = json_data.get('utente', None)
        tipo_utente = json_data.get('tipo_utente', None)
        importo = json_data.get('importo', None)
        articoli = json_data.get('articoli', None)
        id_studente = json_data.get('id_studente', None)
        canale = json_data.get('canale', None)

        self.vendor_class = init_vendor(canale)


        banca = init_bank_json(json_data)

        try:
            intents = crea_payment_intents(articoli, utente, id_studente, anno_scolastico, banca=banca, canale=canale)
            totale = get_totale_intents(intents)
        except Exception as e:
            raise CommandError(f"Errore durante la creazione dei payment intents: {e}")


        payment_intent = self.vendor_class.get_token(totale, intents)
        payment_intent_token = payment_intent['id']

        for intent in intents:
            intent.token = payment_intent_token
            intent.save()