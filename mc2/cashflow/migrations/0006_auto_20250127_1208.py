# Generated by Django 3.1.14 on 2025-01-27 12:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cashflow', '0005_auto_20250124_1655'),
    ]

    operations = [
        migrations.AddField(
            model_name='ccppaymentintents',
            name='payer_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='payer_city',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='payer_fiscal_code',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='payer_name',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='ccppaymentintents',
            name='payer_province',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='ccppaymentintents',
            name='payer_surname',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='payer_zip_code',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='subject_class',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='subject_data',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='subject_school_address',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='ccppaymentintents',
            name='subject_school_address_code',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='ccpmovement',
            name='type',
            field=models.ForeignKey(db_column='type_id', on_delete=django.db.models.deletion.CASCADE, to='cashflow.ccptype'),
        ),
        migrations.AlterField(
            model_name='ccppayment',
            name='covered_movement',
            field=models.ForeignKey(blank=True, db_column='covered_movement_id', null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='covered_movement', to='cashflow.ccpmovement'),
        ),
        migrations.AlterField(
            model_name='ccppayment',
            name='movement',
            field=models.ForeignKey(db_column='movement_id', on_delete=django.db.models.deletion.CASCADE, to='cashflow.ccpmovement'),
        ),
        migrations.AlterField(
            model_name='ccppayment',
            name='payment_method',
            field=models.ForeignKey(db_column='payment_method_id', on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccppaymentmethod'),
        ),
    ]
