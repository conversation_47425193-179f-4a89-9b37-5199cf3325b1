# Generated by Django 3.1.14 on 2024-12-04 10:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CoreBankAccount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country_code', models.CharField(blank=True, max_length=2, null=True)),
                ('check_code', models.Char<PERSON>ield(blank=True, max_length=2, null=True)),
                ('bban', models.Char<PERSON>ield(max_length=100)),
                ('denomination', models.CharField(max_length=100)),
                ('initial_balance', models.FloatField()),
                ('type', models.CharField(max_length=1)),
                ('ise_id', models.IntegerField()),
                ('ise_type', models.Char<PERSON>ield(max_length=1)),
                ('invoice_default', models.BooleanField(blank=True, null=True)),
                ('online_payment_default', models.BooleanField()),
                ('cuc', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('creditor_identifier', models.CharField(blank=True, max_length=255, null=True)),
                ('pvr_number', models.CharField(max_length=20)),
                ('agency_name', models.CharField(blank=True, max_length=50, null=True)),
                ('agency_city', models.CharField(blank=True, max_length=50, null=True)),
                ('agency_zip_code', models.CharField(blank=True, max_length=10, null=True)),
                ('qr_iban', models.CharField(blank=True, max_length=27, null=True)),
                ('qr_bank_account', models.CharField(blank=True, max_length=27, null=True)),
                ('collection_cost', models.FloatField()),
                ('sepa_regex', models.CharField(blank=True, max_length=255, null=True)),
                ('sepa_accent', models.BooleanField(blank=True, null=True)),
                ('piano_conti', models.CharField(blank=True, max_length=255, null=True)),
                ('customer_desc', models.CharField(max_length=255)),
            ],
            options={
                'db_table': 'core_bank_account'
            },
        ),
        migrations.CreateModel(
            name='CcpAdditional',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('positive', models.BooleanField()),
                ('percentual', models.BooleanField()),
                ('payment', models.BooleanField()),
                ('amount', models.FloatField(blank=True, null=True)),
                ('code', models.CharField(blank=True, max_length=20, null=True)),
                ('on_gross', models.BooleanField()),
                ('codice_conto', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'ccp_additional',
            },
        ),
        migrations.CreateModel(
            name='CcpAeCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('row', models.IntegerField()),
                ('incoming', models.BooleanField()),
            ],
            options={
                'db_table': 'ccp_ae_category',
            },
        ),
        migrations.CreateModel(
            name='CcpCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('initial_balance', models.FloatField(default=0)),
            ],
            options={
                'db_table': 'ccp_category',
            },
        ),
        migrations.CreateModel(
            name='CcpCredits',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject_id', models.IntegerField()),
                ('subject_type', models.CharField(blank=True, max_length=1, null=True)),
            ],
            options={
                'db_table': 'ccp_credits',
            },
        ),
        migrations.CreateModel(
            name='CcpDepositSlip',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField()),
                ('date', models.DateTimeField()),
                ('bank_account_iban', models.CharField(max_length=255)),
                ('bank_account_name', models.CharField(max_length=255)),
                ('payment_method_name', models.CharField(max_length=255)),
                ('bank_account_cuc', models.CharField(blank=True, max_length=255, null=True)),
                ('creditor_identifier', models.CharField(blank=True, max_length=255, null=True)),
                ('school_year', models.CharField(blank=True, max_length=9, null=True)),
                ('bank_account', models.ForeignKey(blank=True, db_column='bank_account', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.corebankaccount')),
            ],
            options={
                'db_table': 'ccp_deposit_slip',
            },
        ),
        migrations.CreateModel(
            name='CcpEasySelect',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_method', models.IntegerField(blank=True, null=True)),
                ('force_single_invoice', models.BooleanField(blank=True, null=True)),
            ],
            options={
                'db_table': 'ccp_easy_select',
            },
        ),
        migrations.CreateModel(
            name='CcpInvoice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField()),
                ('date', models.DateTimeField()),
                ('accountholder', models.TextField()),
                ('rows', models.TextField()),
                ('total', models.FloatField()),
                ('bank', models.TextField()),
                ('expiration_date', models.DateTimeField(blank=True, null=True)),
                ('incoming', models.BooleanField()),
                ('xml_name', models.CharField(blank=True, max_length=255, null=True)),
                ('expiration_text', models.TextField(blank=True, null=True)),
                ('table_text', models.TextField(blank=True, null=True)),
                ('ds_name', models.CharField(blank=True, max_length=255, null=True)),
                ('ds_id', models.CharField(blank=True, max_length=255, null=True)),
                ('credit_note', models.BooleanField(blank=True, null=True)),
                ('payment_method', models.IntegerField(blank=True, null=True)),
                ('publication_path', models.CharField(blank=True, max_length=255, null=True)),
                ('header', models.TextField()),
                ('suffix', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'ccp_invoice',
            },
        ),
        migrations.CreateModel(
            name='CcpMovement',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('miscellaneous', models.TextField(blank=True, null=True)),
                ('number', models.IntegerField(blank=True, null=True)),
                ('note', models.TextField(blank=True, null=True)),
                ('school_year', models.CharField(blank=True, max_length=255, null=True)),
                ('subject_id', models.IntegerField(blank=True, null=True)),
                ('subject_data', models.TextField(blank=True, null=True)),
                ('subject_seat', models.IntegerField(blank=True, null=True)),
                ('subject_class', models.CharField(blank=True, max_length=64, null=True)),
                ('amount', models.FloatField()),
                ('creation_date', models.BigIntegerField()),
                ('subject_type', models.CharField(default='S', max_length=1)),
                ('expiration_date', models.BigIntegerField()),
                ('tmp_number', models.TextField(blank=True, null=True)),
                ('subject_school_address_code', models.CharField(blank=True, max_length=255, null=True)),
                ('subject_school_address', models.CharField(blank=True, max_length=255, null=True)),
                ('invoice_code', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.CharField(default='', max_length=255)),
                ('da_ratei', models.DateTimeField(blank=True, null=True)),
                ('a_ratei', models.DateTimeField(blank=True, null=True)),
                ('subject_school_year', models.CharField(blank=True, max_length=255, null=True)),
                ('payment_intent_token', models.CharField(blank=True, max_length=255, null=True)),
                ('payment_intent_id', models.IntegerField(blank=True, null=True)),
                ('locked', models.BooleanField()),
                ('date_published', models.DateTimeField(blank=True, null=True)),
                ('ccp_easy_select', models.ForeignKey(blank=True, db_column='ccp_easy_select', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpeasyselect')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpinvoice')),
            ],
            options={
                'db_table': 'ccp_movement',
            },
        ),
        migrations.CreateModel(
            name='CcpPaymentIntents',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(blank=True, max_length=255, null=True)),
                ('payment_id', models.BigIntegerField(blank=True, null=True)),
                ('payment_object_type', models.CharField(blank=True, max_length=20, null=True)),
                ('payment_object_id', models.BigIntegerField(blank=True, null=True)),
                ('payment_status', models.CharField(blank=True, max_length=255, null=True)),
                ('charge_status', models.CharField(blank=True, max_length=20, null=True)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('payer_type', models.CharField(blank=True, max_length=20, null=True)),
                ('payer_id', models.CharField(blank=True, max_length=30, null=True)),
                ('school_year', models.CharField(blank=True, max_length=9, null=True)),
                ('subject_id', models.BigIntegerField(blank=True, null=True)),
                ('date_created', models.DateTimeField(blank=True, null=True)),
                ('date_succeeded', models.DateTimeField(blank=True, null=True)),
                ('date_canceled', models.DateTimeField(blank=True, null=True)),
                ('date_failed', models.DateTimeField(blank=True, null=True)),
                ('date_charged', models.DateTimeField(blank=True, null=True)),
                ('payment_channel', models.CharField(blank=True, max_length=255, null=True)),
                ('bank_account_id', models.IntegerField()),
                ('date_processed', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'ccp_payment_intents',
            },
        ),
        migrations.CreateModel(
            name='CcpPaymentMethod',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('easy_code', models.IntegerField(blank=True, null=True)),
                ('piano_conti', models.CharField(blank=True, max_length=255, null=True)),
                ('easy_by_bank', models.BooleanField(blank=True, null=True)),
                ('easy_export_grouped', models.BooleanField(blank=True, null=True)),
                ('massive_payment_group', models.BooleanField(blank=True, null=True)),
                ('causale_contabile', models.CharField(blank=True, max_length=50, null=True)),
                ('causale_contabile_uscite', models.CharField(blank=True, max_length=50, null=True)),
                ('data_raggruppamento', models.CharField(blank=True, max_length=255, null=True)),
                ('tipo_raggruppamento', models.CharField(blank=True, max_length=255, null=True)),
                ('piano_conti_da_tipo', models.BooleanField(blank=True, null=True)),
                ('piano_conti_uscite_da_tipo', models.BooleanField(blank=True, null=True)),
            ],
            options={
                'db_table': 'ccp_payment_method',
            },
        ),
        migrations.CreateModel(
            name='CcpPrintCategory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('ordering', models.IntegerField()),
            ],
            options={
                'db_table': 'ccp_print_category',
            },
        ),
        migrations.CreateModel(
            name='CcpReceipt',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.IntegerField()),
                ('date', models.BigIntegerField()),
                ('receipt', models.BooleanField()),
                ('publication_path', models.CharField(blank=True, max_length=255, null=True)),
                ('groupment', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'ccp_receipt',
            },
        ),
        migrations.CreateModel(
            name='CcpReminder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation', models.DateTimeField()),
                ('reminder_type', models.CharField(max_length=255)),
                ('sent', models.DateTimeField(blank=True, null=True)),
                ('mail', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('tried', models.BooleanField()),
                ('confirmed', models.DateTimeField(blank=True, null=True)),
                ('student_text', models.CharField(blank=True, max_length=255, null=True)),
                ('accountholder_text', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'ccp_reminder',
            },
        ),
        migrations.CreateModel(
            name='CcpType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('amount', models.FloatField()),
                ('governative', models.BooleanField()),
                ('expiration_date', models.BigIntegerField(blank=True, null=True)),
                ('cumulative', models.IntegerField(default=0)),
                ('school_year', models.CharField(max_length=9)),
                ('incoming', models.BooleanField()),
                ('section', models.CharField(blank=True, max_length=255, null=True)),
                ('online_payment', models.BooleanField()),
                ('invoice_code', models.CharField(blank=True, max_length=255, null=True)),
                ('easy_code', models.CharField(blank=True, max_length=255, null=True)),
                ('da_ratei', models.DateTimeField(blank=True, null=True)),
                ('a_ratei', models.DateTimeField(blank=True, null=True)),
                ('include_vat', models.BooleanField()),
                ('vat', models.FloatField(blank=True, null=True)),
                ('bollo', models.BooleanField()),
                ('vat_code_id', models.IntegerField(blank=True, null=True)),
                ('easy_description', models.CharField(blank=True, max_length=20, null=True)),
                ('payment_mail', models.CharField(blank=True, max_length=255, null=True)),
                ('online_payment_status', models.IntegerField(blank=True, null=True)),
                ('exclude_corrispettivi', models.BooleanField(blank=True, null=True)),
                ('ccp_credits_type', models.IntegerField(blank=True, null=True)),
                ('centro_costo_ricavo', models.CharField(blank=True, max_length=50, null=True)),
                ('id_importazione', models.CharField(blank=True, max_length=50, null=True)),
                ('pubblica_pagati_online', models.BooleanField(blank=True, null=True)),
                ('include_bollo', models.BooleanField(blank=True, null=True)),
                ('easy_code_contropartita', models.CharField(blank=True, max_length=255, null=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpcategory')),
                ('ccp_ae_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpaecategory')),
            ],
            options={
                'db_table': 'ccp_type',
            },
        ),
        migrations.CreateModel(
            name='CcpVatCode',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(blank=True, max_length=20, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('amount', models.FloatField()),
                ('exemption', models.BooleanField(blank=True, null=True)),
                ('sdi_code', models.CharField(blank=True, max_length=255, null=True)),
                ('easy_code', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'db_table': 'ccp_vat_code',
            },
        ),
        migrations.CreateModel(
            name='CcpTypeStep',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expiration', models.CharField(blank=True, max_length=255, null=True)),
                ('value', models.FloatField(blank=True, null=True)),
                ('description', models.CharField(max_length=255)),
                ('da_ratei', models.DateTimeField(blank=True, null=True)),
                ('a_ratei', models.DateTimeField(blank=True, null=True)),
                ('ccp_type', models.ForeignKey(db_column='ccp_type', on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccptype')),
            ],
            options={
                'db_table': 'ccp_type_step',
            },
        ),
        migrations.CreateModel(
            name='CcpTypeAdditional',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField()),
                ('subject_type', models.CharField(blank=True, max_length=1, null=True)),
                ('subject_id', models.IntegerField(blank=True, null=True)),
                ('discount_order', models.IntegerField(blank=True, null=True)),
                ('additional', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpadditional')),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccptype')),
            ],
            options={
                'db_table': 'ccp_type_additional',
            },
        ),
        migrations.CreateModel(
            name='CcpReminderSubjects',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject_id', models.IntegerField()),
                ('ccp_reminder', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpreminder')),
            ],
            options={
                'db_table': 'ccp_reminder_subjects',
            },
        ),
        migrations.CreateModel(
            name='CcpPayment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.BigIntegerField()),
                ('accountable_date', models.BigIntegerField()),
                ('amount', models.FloatField()),
                ('bollettino', models.CharField(blank=True, max_length=50, null=True)),
                ('account_reference', models.CharField(blank=True, max_length=50, null=True)),
                ('payer_type', models.CharField(max_length=1)),
                ('payer_id', models.CharField(blank=True, max_length=30, null=True)),
                ('payer_name', models.CharField(max_length=50)),
                ('payer_surname', models.CharField(max_length=50)),
                ('payer_fiscal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('payer_address', models.CharField(blank=True, max_length=255, null=True)),
                ('payer_city', models.CharField(blank=True, max_length=255, null=True)),
                ('payer_province', models.CharField(blank=True, max_length=2, null=True)),
                ('payer_zip_code', models.CharField(blank=True, max_length=5, null=True)),
                ('ccp_credit', models.IntegerField(blank=True, null=True)),
                ('ccp_deposit_slip', models.IntegerField(blank=True, null=True)),
                ('easy_import_protocol', models.IntegerField(blank=True, null=True)),
                ('payment_group', models.IntegerField(blank=True, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.corebankaccount')),
                ('covered_movement', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='covered_movement', to='cashflow.ccpmovement')),
                ('movement', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpmovement')),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccppaymentmethod')),
                ('receipt', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpreceipt')),
            ],
            options={
                'db_table': 'ccp_payment',
            },
        ),
        migrations.CreateModel(
            name='CcpMovementAdditional',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField()),
                ('subject_type', models.CharField(blank=True, max_length=1, null=True)),
                ('subject_id', models.IntegerField(blank=True, null=True)),
                ('abs_amount', models.FloatField()),
                ('additional', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpadditional')),
                ('movement', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpmovement')),
            ],
            options={
                'db_table': 'ccp_movement_additional',
            },
        ),
        migrations.AddField(
            model_name='ccpmovement',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccptype'),
        ),
        migrations.CreateModel(
            name='CcpInvoiceTransmission',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transmission_id', models.CharField(max_length=255)),
                ('date', models.DateTimeField()),
                ('status', models.CharField(max_length=2)),
                ('description', models.TextField()),
                ('ccp_invoice', models.ForeignKey(blank=True, db_column='ccp_invoice', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpinvoice')),
            ],
            options={
                'db_table': 'ccp_invoice_transmission',
            },
        ),
        migrations.CreateModel(
            name='CcpInvoiceDepositSlip',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unpaid_date', models.DateTimeField(blank=True, null=True)),
                ('unpaid_note', models.TextField()),
                ('row_number', models.IntegerField()),
                ('total', models.FloatField()),
                ('collection_cost', models.FloatField()),
                ('bollo', models.FloatField()),
                ('movements_total', models.FloatField()),
                ('iban', models.TextField(blank=True, null=True)),
                ('codice_rid', models.TextField(blank=True, null=True)),
                ('data_mandato_rid', models.TextField(blank=True, null=True)),
                ('first_sepa', models.TextField(blank=True, null=True)),
                ('surname', models.CharField(blank=True, max_length=255, null=True)),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=255, null=True)),
                ('province', models.CharField(blank=True, max_length=255, null=True)),
                ('zip_code', models.CharField(blank=True, max_length=255, null=True)),
                ('fiscal_code', models.CharField(blank=True, max_length=255, null=True)),
                ('ccp_deposit_slip', models.ForeignKey(blank=True, db_column='ccp_deposit_slip', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpdepositslip')),
                ('ccp_invoice', models.ForeignKey(blank=True, db_column='ccp_invoice', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpinvoice')),
            ],
            options={
                'db_table': 'ccp_invoice_deposit_slip',
            },
        ),
        migrations.AddField(
            model_name='ccpdepositslip',
            name='payment_method',
            field=models.ForeignKey(blank=True, db_column='payment_method', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccppaymentmethod'),
        ),
        migrations.CreateModel(
            name='CcpDeposits',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.BigIntegerField()),
                ('accountable_date', models.BigIntegerField(blank=True, null=True)),
                ('amount', models.FloatField()),
                ('payment_method_id', models.IntegerField(blank=True, null=True)),
                ('payer_type', models.CharField(blank=True, max_length=1, null=True)),
                ('payer_id', models.CharField(blank=True, max_length=30, null=True)),
                ('payer_name', models.CharField(blank=True, max_length=50, null=True)),
                ('payer_surname', models.CharField(blank=True, max_length=50, null=True)),
                ('payer_fiscal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('payer_address', models.CharField(blank=True, max_length=80, null=True)),
                ('payer_city', models.CharField(blank=True, max_length=30, null=True)),
                ('payer_province', models.CharField(blank=True, max_length=2, null=True)),
                ('payer_zip_code', models.CharField(blank=True, max_length=5, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('deposit_type', models.CharField(blank=True, max_length=1, null=True)),
                ('tx_id', models.CharField(blank=True, max_length=255, null=True)),
                ('credits', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpcredits')),
            ],
            options={
                'db_table': 'ccp_deposits',
            },
        ),
        migrations.CreateModel(
            name='CcpCreditsType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('is_default', models.BooleanField(blank=True, null=True)),
                ('active', models.BooleanField(blank=True, null=True)),
                ('discount', models.BooleanField()),
                ('dote', models.IntegerField()),
                ('exclude_corrispettivi', models.BooleanField(blank=True, null=True)),
                ('movement', models.BooleanField()),
                ('piano_conti', models.CharField(blank=True, max_length=255, null=True)),
                ('causale_contabile', models.CharField(blank=True, max_length=255, null=True)),
                ('causale_contabile_uscite', models.CharField(blank=True, max_length=255, null=True)),
                ('show_on_site', models.BooleanField()),
                ('ccp_payment_method', models.ForeignKey(blank=True, db_column='ccp_payment_method', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccppaymentmethod')),
            ],
            options={
                'db_table': 'ccp_credits_type',
            },
        ),
        migrations.AddField(
            model_name='ccpcredits',
            name='credit_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpcreditstype'),
        ),
        migrations.CreateModel(
            name='CcpCategoryBanks',
            fields=[
                ('category_id', models.IntegerField(primary_key=True, serialize=False)),
                ('bank_id', models.IntegerField()),
                ('initial_balance', models.FloatField()),
            ],
            options={
                'db_table': 'ccp_category_banks',
                'unique_together': {('category_id', 'bank_id')},
            },
        ),
        migrations.CreateModel(
            name='CcpAdditionalTemplates',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('additional', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpadditional')),
                ('type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccptype')),
            ],
            options={
                'db_table': 'ccp_additional_templates',
            },
        ),
        migrations.CreateModel(
            name='CcpPrintCategoryMovementType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ccp_print_category', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpprintcategory')),
                ('ccp_type', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccptype')),
            ],
            options={
                'db_table': 'ccp_print_category_movement_type',
                'unique_together': {('ccp_print_category', 'ccp_type')},
            },
        ),
        migrations.CreateModel(
            name='CcpPaymentAdditional',
            fields=[
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.DO_NOTHING, primary_key=True, serialize=False, to='cashflow.ccppayment')),
                ('amount', models.FloatField()),
                ('additional', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cashflow.ccpadditional')),
            ],
            options={
                'db_table': 'ccp_payment_additional',
                'unique_together': {('payment', 'additional')},
            },
        ),
    ]
