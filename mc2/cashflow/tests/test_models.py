from django.test import TestCase
from ..models import CcpCategory as Category
from ..models import CcpType as MovementType
from ..models import CcpTypeStep as MovementTypeStep
from ..models import CcpMovement as Movement
from ..models import CcpAeCategory as AeCategory
from ..models import CcpPaymentIntents as PaymentIntents


from .factories import CategoryFactory, BankAccountFactory, MovementTypeFactory, MovementFactory, PaymentFactory, PaymentMethodFactory, PaymentIntentsFactory, MovementTypeStepFactory


from datetime import datetime
from decimal import Decimal
from django.utils import timezone

class CategoryModelTest(TestCase):
    def test_category_str(self):
        category = CategoryFactory(name='Test Category')
        self.assertEqual(category.__str__(), 'Test Category')


class MovementTypeModelTest(TestCase):
    def test_movement_type_str(self):
        movement_type = MovementTypeFactory(
            name='Type Test',
            school_year='2024/2025',
            category=CategoryFactory(name='Test Category'),
            incoming=True
        )
        self.assertEqual(movement_type.__str__(), 'Type Test - 2024/2025 - Test Category')


class PaymentIntentsTestCase(TestCase):
    def setUp(self):
        self.payment_intent_with_date = PaymentIntentsFactory(
            date_succeeded=timezone.now()
        )
        self.payment_intent_without_date = PaymentIntentsFactory(
            date_succeeded=None
        )
        self.movement_type = MovementTypeFactory(name='Test Type',  id=2)
        self.movement_type_step = MovementTypeStepFactory(ccp_type=self.movement_type, value=3.33)
        self.movement = MovementFactory(description='Test Movement', amount=100.00, id=1)
        self.payment_intent_token_1 = PaymentIntentsFactory(token="test_token", amount=100.00, payment_object_type='movement', payment_object_id=self.movement.id)
        self.payment_intent_token_2 = PaymentIntentsFactory(token="test_token", amount=3.33, payment_object_type='movement_type', payment_object_id=self.movement_type.id)
        self.payment_intent_token_3 = PaymentIntentsFactory(token="test_token_2", amount=99.00)


    def test_succeeded_with_date(self):
        self.assertTrue(self.payment_intent_with_date.succeeded())

    def test_succeeded_without_date(self):
        self.assertFalse(self.payment_intent_without_date.succeeded())


    def test_transaction_total(self):
        # Retrieve the payment intent with the test token
        payment_intent = PaymentIntents.objects.filter(token="test_token").first()

        # Calculate the total amount for the test token
        total = payment_intent.transaction_total()

        # Assert the total amount is correct
        test_amount = Decimal('103.33')
        self.assertEqual(total, test_amount)


        payment_intent = PaymentIntents.objects.filter(token="test_token_2").first()
        total = payment_intent.transaction_total()
        self.assertEqual(total, 99.00)


    def test_items(self):
        # Retrieve the payment intent with the test token
        payment_intent = PaymentIntents.objects.filter(token="test_token").first()

        # Get the items related to the payment intent
        items = payment_intent.items()

        # Assert the items are correct
        self.assertEqual(len(items), 2)
        self.assertEqual(items[0]['id'], 1)
        self.assertEqual(items[0]['tipo'], 'movimento')
        self.assertEqual(items[0]['totale'], 100.00)
        self.assertEqual(items[0]['descrizione'], 'Test Movement')
        self.assertEqual(items[1]['id'], 2)
        self.assertEqual(items[1]['tipo'], 'tipo_movimento')
        self.assertEqual(items[1]['totale'], 3.33)
        self.assertEqual(items[1]['descrizione'], 'Test Type')


class PaymentTest(TestCase):

    def test_get_piano_conti_with_no_bank(self):
        payment = PaymentFactory(
            payment_method=PaymentMethodFactory(easy_by_bank=False, piano_conti='2'),
            account=BankAccountFactory(piano_conti='1')
        )
        self.assertEqual(payment.get_piano_conti(), '2')

    def test_get_piano_conti_with_bank(self):
        payment = PaymentFactory(
            payment_method=PaymentMethodFactory(easy_by_bank=True, piano_conti='2'),
            account=BankAccountFactory(piano_conti='1')
        )
        self.assertEqual(payment.get_piano_conti(), '1')