from django.test import TestCase
from .factories import PaymentFactory
from mc2.cashflow.movements.utils import get_list


class TestGetList(TestCase):

    def test_get_list(self):
        PaymentFactory(id=1)
        PaymentFactory(id=2)
        PaymentFactory(id=3)
        filter = {'ids': [1, 2]}
        payments = get_list(filter)
        self.assertEqual(len(payments), 2)
        self.assertEqual(set(payment.id for payment in payments), {1, 2})
