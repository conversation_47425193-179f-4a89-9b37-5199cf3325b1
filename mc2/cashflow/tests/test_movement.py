import datetime
from django.test import TestCase
from mc2.cashflow.movements.utils import create
from mc2.cashflow.models import CcpCredits, CcpCreditsType
from .factories import MovementTypeFactory, MovementFactory, CreditTypeFactory
from mc2.cashflow.models.movement import CcpMovement
from mc2.cashflow.movements.utils import get_list

class TestMovement(TestCase):

    def setUp(self):
        self.movement_type = MovementTypeFactory()
        self.credit_type = CreditTypeFactory()


    def test_create(self):

        data = {
            'amount': 100,
            'expiration_date': int(datetime.datetime.now().timestamp()),
            'creation_date': int(datetime.datetime.now().timestamp()),
            'type': self.movement_type,
            'subject_type': 'S',
            'locked': False,
            'subject_school_year': '2020/2021',
        }
        movement = create(data)
        self.assertEqual(movement.amount, 100)
        self.assertEqual(movement.subject_type, 'S')
        self.assertEqual(movement.locked, False)
        self.assertEqual(movement.subject_school_year, '2020/2021')


    def test_create_credit(self):

        credit = CcpCredits.objects.filter(subject_id=100, credit_type=self.credit_type)
        self.assertEqual(list(credit), [])
        credit_type = CcpCreditsType.objects.get(id=self.credit_type.id)
        self.assertEqual(credit_type.movement, False)
        movement_type_credit = MovementTypeFactory(ccp_credits_type=self.credit_type.id)
        data = {
            'amount': 100,
            'expiration_date': int(datetime.datetime.now().timestamp()),
            'creation_date': int(datetime.datetime.now().timestamp()),
            'type': movement_type_credit,
            'subject_type': 'S',
            'subject_id': 100,
            'locked': False,
            'subject_school_year': '2020/2021',
        }

        movement = create(data)
        self.assertEqual(movement.amount, 100)
        self.assertEqual(movement.subject_type, 'S')
        self.assertEqual(movement.locked, False)
        self.assertEqual(movement.subject_school_year, '2020/2021')


        credit = CcpCredits.objects.get(subject_id=100, credit_type=self.credit_type)
        credit_type = CcpCreditsType.objects.get(id=self.credit_type.id)
        self.assertIsNotNone(credit)
        self.assertEqual(credit.subject_id, 100)
        self.assertEqual(credit.credit_type, self.credit_type)
        self.assertEqual(credit_type.movement, True)


class TestGetList(TestCase):

    def test_get_list(self):
        MovementFactory(id=1)
        MovementFactory(id=2)
        MovementFactory(id=3)
        filter = {'ids': [1, 2]}
        movements = get_list(filter)
        self.assertEqual(len(movements), 2)  # Assuming no movements with these IDs exist
        self.assertEqual(set(movement.id for movement in movements), {1, 2})
