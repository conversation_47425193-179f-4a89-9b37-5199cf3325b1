import factory
import datetime
from ..models import CcpMovement as Movement
from ..models import CcpPayment as Payment
from ..models import CcpPaymentMethod as PaymentMethod
from ..models import CcpType as MovementType
from ..models import CcpTypeStep as MovementTypeStep
from ..models import CcpCategory as Category
from ..models import CcpAeCategory as AeCategory
from ..models import CoreBankAccount as BankAccount
from ..models import CcpPaymentIntents as PaymentIntents
from ..models import CcpCredits as Credit
from ..models import CcpCreditsType as CreditType

class BankAccountFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BankAccount

    denomination = factory.Sequence(lambda n: 'Bank account %s' % n)
    online_payment_default = False
    invoice_default = False
    initial_balance = 0
    ise_id = 1
    ise_type = 'S'
    bban = '***************************'
    pvr_number = ''
    collection_cost = 0
    customer_desc = ''


class CategoryFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Category

    name = factory.Sequence(lambda n: 'Category %s' % n)
    initial_balance = 0


class AeCategoryFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AeCategory

    description = factory.Sequence(lambda n: 'Ae Category %s' % n)
    row = 0
    incoming = True


class MovementTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MovementType

    name = factory.Sequence(lambda n: 'Movement type %s' % n)
    amount = 0
    governative = False
    cumulative = 0
    school_year = '2020/2021'
    category = factory.SubFactory(CategoryFactory)
    incoming = True
    online_payment = False
    include_vat = False
    bollo = False
    online_payment_status = 0
    exclude_corrispettivi = False
    pubblica_pagati_online = True
    include_bollo = False
    easy_code = 1


class MovementTypeStepFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = MovementTypeStep

    ccp_type = factory.SubFactory(MovementTypeFactory)
    expiration = '2020-12-31'
    value = 3.33
    description = 'Test description'


class MovementFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Movement

    amount = 100
    expiration_date = int(datetime.datetime.now().timestamp())
    creation_date = int(datetime.datetime.now().timestamp())
    type = factory.SubFactory(MovementTypeFactory)
    subject_type = 'S'
    locked = False
    subject_school_year = '2020/2021'


class PaymentMethodFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = PaymentMethod

    name = factory.Sequence(lambda n: 'Payment method %s' % n)
    easy_code = 1
    piano_conti = '1'


class PaymentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Payment

    movement = factory.SubFactory(MovementFactory)
    operation_date = int(datetime.datetime.now().timestamp())
    accountable_date = int(datetime.datetime.now().timestamp())
    amount = 100
    payment_method = factory.SubFactory(PaymentMethodFactory)
    account = factory.SubFactory(BankAccountFactory)
    payer_name = 'Payer name'
    payer_surname = 'Payer surname'
    payer_type = 'S'



class PaymentIntentsFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = PaymentIntents

    bank_account_id = 1
    token = factory.Sequence(lambda n: 'token_%s' % n)


class CreditTypeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CreditType

    description = factory.Sequence(lambda n: 'Credit type %s' % n)
    is_default = False
    active = True
    discount = False
    dote = 0
    movement = False
    show_on_site = True


class CreditFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Credit


