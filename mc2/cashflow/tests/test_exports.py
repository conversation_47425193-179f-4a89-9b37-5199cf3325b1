from datetime import datetime
from unittest.mock import patch

from django.urls import reverse
from django.test import TestCase
from django.db.models import QuerySet
from django.http import FileResponse

from mc2.common.tests.factories import InstituteFactory
from ..tests.factories import PaymentFactory, MovementFactory, MovementTypeFactory
from ..exports.bpoint import Movim
from ..exports.bpoint import BPointFilterForm
from ..exports.bpoint import BPointExportAPIView
from ..models import CcpPayment as Payment
from rest_framework.test import APIClient


class BPointFilterFormTest(TestCase):
    def test_clean_filter_passing_only_one_key(self):
        filter = BPointFilterForm({'movement_ids': '1,2'})
        self.assertTrue(filter.is_valid())
        self.assertEqual(filter.cleaned_data['movement_ids'], [1, 2])
        self.assertEqual(filter.cleaned_data['payment_ids'], [])

    def test_clean_filter_passing_both_keys_but_one_empty(self):
        filter = BPointFilterForm({'movement_ids': '', 'payment_ids': '3,4'})
        self.assertTrue(filter.is_valid())
        self.assertEqual(filter.cleaned_data['payment_ids'], [3, 4])
        self.assertEqual(filter.cleaned_data['movement_ids'], [])

    def test_both_keys_populated_error(self):
        filter = BPointFilterForm({'movement_ids': '1,2', 'payment_ids': '3,4'})
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'__all__': ['Only one of movement_ids or payment_ids must be provided.']})

    def test_no_keys_populated_erro(self):
        filter = BPointFilterForm({'movement_ids': '', 'payment_ids': ''})
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'__all__': ['At least one of movement_ids or payment_ids must be provided.']})

    def test_bad_movement_ids_value_error(self):
        filter = BPointFilterForm({'movement_ids': 'BAD VALUE'})
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'movement_ids': ['Enter a valid list of integers.']})

    def test_bad_payment_ids_value_error(self):
        filter = BPointFilterForm({'payment_ids': 'BAD VALUE'})
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'payment_ids': ['Enter a valid list of integers.']})

    def test_empty_dict_filter(self):
        filter = BPointFilterForm(dict())
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'__all__': ['At least one of movement_ids or payment_ids must be provided.']})

    def test_None_filter(self):
        filter = BPointFilterForm(None)
        self.assertFalse(filter.is_valid())
        self.assertEqual(filter.errors, {'__all__': ['At least one of movement_ids or payment_ids must be provided.']})



class BPointViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse('bpoint-export-view')

    @patch('mc2.permissions.IsLocalhost.has_permission', return_value=True)
    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_base_request(self, mock_permission, mock_get_students_personal_fields):
        response = self.client.get(self.url, {'movement_ids': '1,2'})
        self.assertTrue(isinstance(response, FileResponse))

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_partita_same_movement_two_payments(self, mock_get_students_personal_fields):
        InstituteFactory()
        m1 = MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(centro_costo_ricavo='1', easy_code='1')
        )
        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )
        PaymentFactory(
            id=2,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )

        exporter = BPointExportAPIView()
        data = exporter.build({'movement_ids': [1]})
        self.assertEqual(data[0]['partita'], 1)

        exporter = BPointExportAPIView()
        data = exporter.build({'payment_ids': [1,2]})
        self.assertEqual(data[0]['partita'], 1)
        self.assertEqual(data[1]['partita'], 2)



    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_multiple_movements_check_internal_code_not_increment(self, mock_get_students_personal_fields):
        InstituteFactory()
        m1 = MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(centro_costo_ricavo='1', easy_code='1')
        )
        m2 = MovementFactory(
            id=2,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(centro_costo_ricavo='1', easy_code='1')
        )

        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )
        PaymentFactory(
            id=2,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m2
        )

        exporter = BPointExportAPIView()
        data = exporter.build({'movement_ids': [1,2]})
        self.assertEqual(data[0]['internal_counter'], 1)
        self.assertEqual(data[1]['internal_counter'], 2)

        data = exporter.build({'payment_ids': [1,2]})
        self.assertEqual(data[0]['internal_counter'], 1)
        self.assertEqual(data[1]['internal_counter'], 2)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_build_one_payment_ok(self, mock_get_students_personal_fields):

        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100
        )
        InstituteFactory()

        view = BPointExportAPIView()
        data = view.build({'payment_ids': [1]})
        exporter = Movim(data)
        self.assertTrue(exporter.validate())
        self.assertEqual(len(exporter.rows), 1)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_build_one_movement_ok(self, mock_get_students_personal_fields):
        MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(centro_costo_ricavo='1', easy_code='1')
        )
        InstituteFactory()

        view = BPointExportAPIView()
        data = view.build({'movement_ids': [1]})
        exporter = Movim(data)
        self.assertTrue(exporter.validate())
        self.assertEqual(len(exporter.rows), 1)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_multiple_movements_check_internal_code_increment(self, mock_get_students_personal_fields):
        InstituteFactory()
        m1 = MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(centro_costo_ricavo='1', easy_code='1')
        )

        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )
        PaymentFactory(
            id=2,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )

        exporter = BPointExportAPIView()
        data = exporter.build({'movement_ids': [1]})
        self.assertEqual(data[0]['internal_counter'], 1)

        data = exporter.build({'payment_ids': [1,2]})
        self.assertEqual(data[0]['internal_counter'], 1)
        self.assertEqual(data[1]['internal_counter'], 1)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_annotation(self, mock_get_students_personal_fields):
        InstituteFactory()
        m1 = MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(
                name='Type test',
                centro_costo_ricavo='1',
                easy_code='1',
            )
        )

        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=100,
            movement=m1
        )


        exporter = BPointExportAPIView()
        data = exporter.build({'payment_ids': [1]})
        self.assertEqual(data[0]['annotations'], 'Type test') # DARE

        data = exporter.build({'movement_ids': [1]})
        self.assertEqual(data[0]['annotations'], 'Type test') # AVERE

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_payment_institute_fiscal_code(self, mock_get_students_personal_fields):
        InstituteFactory(school_fiscal_code='school_fiscal_code', fiscal_code='fiscal_code')
        PaymentFactory(id=1)
        exporter = BPointExportAPIView()
        data = exporter.build({'payment_ids': [1]})
        self.assertEqual(data[0]['fiscal_code'], 'school_fiscal_code')

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_movement_institute_fiscal_code(self, mock_get_students_personal_fields):
        InstituteFactory(school_fiscal_code='school_fiscal_code', fiscal_code='fiscal_code')
        MovementFactory(id=1, type=MovementTypeFactory(easy_code='1'))
        exporter = BPointExportAPIView()
        data = exporter.build({'movement_ids': [1]})
        self.assertEqual(data[0]['fiscal_code'], 'school_fiscal_code')

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_get_data_one_payment_of_two_in_filter(self, mock_get_students_personal_fields):
        PaymentFactory(id=1)
        PaymentFactory(id=2)
        exporter = BPointExportAPIView()
        payments = exporter.get_data({'payment_ids': [1]})
        self.assertEqual(len(payments), 1)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_get_data_one_movement_of_two_in_filter(self, mock_get_students_personal_fields):
        MovementFactory(id=1)
        MovementFactory(id=2)
        exporter = BPointExportAPIView()
        movements = exporter.get_data({'movement_ids': [1]})
        self.assertEqual(len(movements), 1)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_get_data_all_payment_in_filter(self, mock_get_students_personal_fields):
        PaymentFactory(id=1)
        PaymentFactory(id=2)
        exporter = BPointExportAPIView()
        payments = exporter.get_data({'payment_ids': [1, 2]})
        self.assertEqual(len(payments), 2)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_get_data_all_movements_in_filter(self, mock_get_students_personal_fields):
        MovementFactory(id=1)
        MovementFactory(id=2)
        exporter = BPointExportAPIView()
        movements = exporter.get_data({'movement_ids': [1, 2]})
        self.assertEqual(len(movements), 2)

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_get_sign(self, mock_get_students_personal_fields):
        exporter = BPointExportAPIView()
        self.assertEqual(exporter.get_sign(True, 100), 'P')
        self.assertEqual(exporter.get_sign(False, 100), 'N')
        self.assertEqual(exporter.get_sign(True, -100), 'N')
        self.assertEqual(exporter.get_sign(False, -100), 'N')

    @patch('mc2.cashflow.exports.bpoint.BPointExportAPIView.get_students_personal_fields', return_value=[])
    def test_amount_always_positive(self, mock_get_students_personal_fields):
        InstituteFactory()
        m1 = MovementFactory(
            id=1,
            subject_school_year='2020/2021',
            amount=100,
            type=MovementTypeFactory(
                name='Type test',
                centro_costo_ricavo='1',
                easy_code='1',
            )
        )
        PaymentFactory(
            id=1,
            accountable_date=datetime(2021, 1, 1).timestamp(),
            amount=-100,
            movement=m1
        )
        exporter = BPointExportAPIView()
        data = exporter.build({'payment_ids': [1]})
        self.assertEqual(data[0]['amount'], 10000)
        self.assertEqual(data[0]['amount_sign'], 'N')

        exporter = BPointExportAPIView()
        data = exporter.build({'movement_ids': [1]})
        self.assertEqual(data[0]['amount'], 10000)
        self.assertEqual(data[0]['amount_sign'], 'P')