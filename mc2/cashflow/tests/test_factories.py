from django.test import TestCase
from .factories import CategoryFactory, MovementTypeFactory, MovementFactory, AeCategoryFactory, BankAccountFactory, PaymentFactory
from ..models import CcpCategory as Category, CcpType as MovementType, CcpMovement as Movement, CcpAeCategory as AeCategory, CoreBankAccount as BankAccount
from ..models import CcpPayment as Payment

class BankAccountFactoryTest(TestCase):
    def test_bank_account_base_creation(self):
        bank_account = BankAccountFactory()
        self.assertIsInstance(bank_account, BankAccount)
        self.assertTrue(bank_account.denomination.startswith('Bank account'))
        self.assertFalse(bank_account.online_payment_default)
        self.assertFalse(bank_account.invoice_default)
class CategoryFactoryTest(TestCase):
    def test_category_base_creation(self):
        category = CategoryFactory()
        self.assertIsInstance(category, Category)
        self.assertEqual(category.initial_balance, 0)
        self.assertTrue(category.name.startswith('Category'))

class MovementTypeFactoryTest(TestCase):
    def test_movement_type_base_creation(self):
        movement_type = MovementTypeFactory()
        self.assertIsInstance(movement_type, MovementType)
        self.assertEqual(movement_type.amount, 0)
        self.assertFalse(movement_type.governative)
        self.assertEqual(movement_type.cumulative, 0)
        self.assertEqual(movement_type.school_year, '2020/2021')
        self.assertIsInstance(movement_type.category, Category)
        self.assertTrue(movement_type.incoming)
        self.assertFalse(movement_type.online_payment)
        self.assertFalse(movement_type.include_vat)
        self.assertFalse(movement_type.bollo)
        self.assertEqual(movement_type.online_payment_status, 0)
        self.assertFalse(movement_type.exclude_corrispettivi)
        self.assertTrue(movement_type.pubblica_pagati_online)
        self.assertFalse(movement_type.include_bollo)

class MovementFactoryTest(TestCase):
    def test_movement_base_creation(self):
        movement = MovementFactory()
        self.assertIsInstance(movement, Movement)
        self.assertEqual(movement.amount, 100)
        self.assertIsInstance(movement.expiration_date, int)
        self.assertIsInstance(movement.creation_date, int)
        self.assertEqual(movement.subject_type, 'S')
        self.assertFalse(movement.locked)


class AeCategoryFactoryTest(TestCase):
    def test_ae_category_base_creation(self):
        ae_category = AeCategoryFactory()
        self.assertIsInstance(ae_category, AeCategory)
        self.assertTrue(ae_category.description.startswith('Ae Category'))
        self.assertEqual(ae_category.row, 0)
        self.assertTrue(ae_category.incoming)


class PaymentFatcoryTest(TestCase):
    def test_payment_base_creation(self):
        payment = PaymentFactory()
        self.assertIsInstance(payment, Payment)
        self.assertIsInstance(payment.movement, Movement)
        self.assertEqual(payment.amount, 100)
        self.assertIsInstance(payment.operation_date, int)
        self.assertIsInstance(payment.account, BankAccount)
        self.assertEqual(payment.payer_type, 'S')

