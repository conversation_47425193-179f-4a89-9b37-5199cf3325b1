from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from unittest.mock import patch
from mc2.cashflow.payment_intents.views import PaymentIntentViewSet
from mc2.cashflow.models import CcpPaymentIntents as PaymentIntent
from mc2.cashflow.payment_intents.utils import crea_payment_intents
from .factories import BankAccountFactory, MovementFactory, MovementTypeFactory, MovementTypeStepFactory
import base64


class MockVendor:
    def __init__(self):
        pass
    def get_publishable_key(self):
        return None
    def get_token(self, amount, intents):
        return {'id': 'test_payment_intent_token'}

    def get_channel_intents_data(self):
        return [
            {'name': 'channel_key', 'value': 'test_key_id'},
            {'name': 'satispay_private_key', 'value': base64.b64encode(b'test_private_key')}
        ]

class MockVendorStripe(MockVendor):
    def get_publishable_key(self):
        return 'test_publishable_key'

    def get_channel_intents_data(self):
        return [
            {'name': 'channel_key', 'value': 'test_key_id'},
            {'name': 'satispay_private_key', 'value': base64.b64encode(b'test_private_key')}
        ]
class PaymentIntentViewSetTests(TestCase):

    def setUp(self):
        self.client = APIClient()
        self.url = reverse('payment_intent-list')
        self.bank = BankAccountFactory(online_payment_default=True)
        self.item1 = MovementFactory()
        self.item2 = MovementTypeFactory()
        self.item3 = MovementTypeFactory()
    def mock_init_vendor(self, canale):
        return True

    def test_create_missing_client_info(self):
        response = self.client.post(self.url, {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['messaggio'], 'client info missing in header')

    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    def test_create_invalid_channel(self, mock_init_vendor):
        mock_init_vendor.return_value = None
        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        response = self.client.post(self.url, {}, format='json', **headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['messaggio'], 'channel not valid')

    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    def test_create_invalid_bank(self, mock_init_bank, mock_init_vendor):
        mock_init_vendor.return_value = True
        mock_init_bank.return_value = None
        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        response = self.client.post(self.url, {}, format='json', **headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['messaggio'], 'bank not valid')

    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    def test_create_missing_anno_scolastico(self, mock_init_bank, mock_init_vendor):
        mock_init_vendor.return_value = True
        mock_init_bank.return_value = True
        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        data = {
            'utente': 'test_user',
            'tipo_utente': 'test_type',
            'importo': 100,
            'articoli': ['item1', 'item2'],
            'id_studente': 'student_id',
            'banca': 1,
            'canale': 'test_channel'
        }
        response = self.client.post(self.url, data, format='json', **headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['messaggio'], 'anno scolastico is required')

    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    @patch('mc2.cashflow.payment_intents.views.crea_payment_intents')
    @patch('mc2.cashflow.payment_intents.views.get_totale_intents')
    def test_create_successful_satispay(self, mock_get_totale_intents, mock_crea_payment_intents, mock_init_bank, mock_init_vendor):

        mock_init_vendor.return_value = MockVendor()
        mock_init_bank.return_value = self.bank
        mock_crea_payment_intents.return_value = [PaymentIntent(id=1, bank_account_id=self.bank.id), PaymentIntent(id=2, bank_account_id=self.bank.id)]
        mock_get_totale_intents.return_value = 100

        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        data = {
            'utente': 'test_user',
            'tipo_utente': 'test_type',
            'importo': 100,
            'articoli': [
                {
                    'tipo': 'movimento',
                    'id': self.item1.id,
                    'amount': 10.10
                },
                {
                    'tipo': 'tipo_movimento',
                    'id': self.item2.id,
                    'amount': 20.20
                }
            ],
            'id_studente': 'student_id',
            'banca': self.bank.id,
            'canale': {
                'nome': 'SATISPAY',
                'config': {
                    'env': 'PRODUCTION',
                    'key_id': 'test_key_id',
                    'private_key': base64.b64encode(b'test_private_key'),
                    'callback_url': 'test_callback_url',
                    },
                },
            'anno_scolastico': '2023_2024'
        }
        response = self.client.post(self.url, data, format='json', **headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertIn('transaction_id', response.data)
        self.assertEqual(response.data['transaction_id'], 'test_payment_intent_token')


    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    @patch('mc2.cashflow.payment_intents.views.crea_payment_intents')
    @patch('mc2.cashflow.payment_intents.views.get_totale_intents')
    def test_create_successful_satispay_decimal(self, mock_get_totale_intents, mock_crea_payment_intents, mock_init_bank, mock_init_vendor):

        mock_init_vendor.return_value = MockVendor()
        mock_init_bank.return_value = self.bank
        mock_crea_payment_intents.return_value = [PaymentIntent(id=1, bank_account_id=self.bank.id), PaymentIntent(id=2, bank_account_id=self.bank.id)]
        mock_get_totale_intents.return_value = 100

        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        data = {
            'utente': 'test_user',
            'tipo_utente': 'test_type',
            'importo': 0.5,
            'articoli': [
                {
                    'tipo': 'tipo_movimento',
                    'id': self.item2.id
                }
            ],
            'id_studente': 'student_id',
            'banca': self.bank.id,
            'canale': {
                'nome': 'SATISPAY',
                'config': {
                    'env': 'PRODUCTION',
                    'key_id': 'test_key_id',
                    'private_key': base64.b64encode(b'test_private_key'),
                    'callback_url': 'test_callback_url',
                    },
                },
            'anno_scolastico': '2023_2024'
        }
        response = self.client.post(self.url, data, format='json', **headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertIn('transaction_id', response.data)
        self.assertEqual(response.data['transaction_id'], 'test_payment_intent_token')


    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    @patch('mc2.cashflow.payment_intents.views.crea_payment_intents')
    @patch('mc2.cashflow.payment_intents.views.get_totale_intents')
    def test_create_successful_stripe(self, mock_get_totale_intents, mock_crea_payment_intents, mock_init_bank, mock_init_vendor):

        mock_init_vendor.return_value = MockVendorStripe()
        mock_init_bank.return_value = self.bank
        mock_crea_payment_intents.return_value = [PaymentIntent(id=1, bank_account_id=self.bank.id), PaymentIntent(id=2, bank_account_id=self.bank.id)]
        mock_get_totale_intents.return_value = 100

        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        data = {
            'utente': 'test_user',
            'tipo_utente': 'test_type',
            'importo': 100,
            'articoli': [
                {
                    'tipo': 'movimento',
                    'id': self.item1.id,
                    'amount': 10.10
                },
                {
                    'tipo': 'tipo_movimento',
                    'id': self.item2.id,
                    'amount': 20.20
                }
            ],
            'id_studente': 'student_id',
            'banca': self.bank.id,
            'canale': {
                'nome': 'SATISPAY',
                'config': {
                    'env': 'PRODUCTION',
                    'key_id': 'test_key_id',
                    'private_key': base64.b64encode(b'test_private_key'),
                    'callback_url': 'test_callback_url',
                    },
                },
            'anno_scolastico': '2023_2024'
        }
        response = self.client.post(self.url, data, format='json', **headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertIn('transaction_id', response.data)
        self.assertIn('stripe_publishable_key', response.data)
        self.assertEqual(response.data['transaction_id'], 'test_payment_intent_token')
        self.assertEqual(response.data['stripe_publishable_key'], 'test_publishable_key')



    @patch('mc2.cashflow.payment_intents.views.init_vendor')
    @patch('mc2.cashflow.payment_intents.views.init_bank')
    @patch('mc2.cashflow.payment_intents.views.crea_payment_intents')
    @patch('mc2.cashflow.payment_intents.views.get_totale_intents')
    def test_create_intents_qty(self, mock_get_totale_intents, mock_crea_payment_intents, mock_init_bank, mock_init_vendor):

        mock_init_vendor.return_value = MockVendorStripe()
        mock_init_bank.return_value = self.bank
        mock_crea_payment_intents.return_value = [PaymentIntent(id=1, bank_account_id=self.bank.id), PaymentIntent(id=2, bank_account_id=self.bank.id)]
        mock_get_totale_intents.return_value = 100

        headers = {
            'HTTP_X-Client-Name': 'test_client',
            'HTTP_X-Client-Version': '1.0',
            'HTTP_X-Client-Platform': 'test_platform'
        }
        data = {
            'utente': 'test_user',
            'tipo_utente': 'test_type',
            'importo': 100,
            'articoli': [
                {
                    'tipo': 'movimento',
                    'id': self.item1.id,
                    'amount': 10.10
                },
                {
                    'tipo': 'tipo_movimento',
                    'qta': 2,
                    'id': self.item2.id,
                    'amount': 20.20
                }
            ],
            'id_studente': 'student_id',
            'banca': self.bank.id,
            'canale': {
                'nome': 'SATISPAY',
                'config': {
                    'env': 'PRODUCTION',
                    'key_id': 'test_key_id',
                    'private_key': base64.b64encode(b'test_private_key'),
                    'callback_url': 'test_callback_url',
                    },
                },
            'anno_scolastico': '2023_2024'
        }
        response = self.client.post(self.url, data, format='json', **headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertIn('transaction_id', response.data)
        self.assertIn('stripe_publishable_key', response.data)
        self.assertEqual(response.data['transaction_id'], 'test_payment_intent_token')
        self.assertEqual(response.data['stripe_publishable_key'], 'test_publishable_key')



class PaymentIntentUtilsTests(TestCase):

    def setUp(self):
        self.bank = BankAccountFactory(online_payment_default=True)
        self.item1 = MovementFactory(amount=10.10, school_year='2023/2024')
        self.item2 = MovementTypeFactory()
        self.item2step = MovementTypeStepFactory(ccp_type=self.item2, value=20.20)
        self.item3 = MovementTypeFactory()
        self.item3step = MovementTypeStepFactory(ccp_type=self.item3, value=30.30)
        self.request = {'canale': {'nome': 'test_channel'}}

    # Test crea_payment_intents function

    def test_crea_payment_intents(self):
        articoli = [
            {
                'tipo': 'movimento',
                'id': self.item1.id,
                'amount': 10.10,
                'qta': 1
            },
            {
                'tipo': 'tipo_movimento',
                'id': self.item2.id,
                'amount': 20.20,
                'qta': 1
            }
        ]
        intents = crea_payment_intents(articoli, 'test_user', 'student_id', '2023_2024', banca=self.bank, request=self.request)
        self.assertEqual(len(intents), 2)
        self.assertEqual(intents[0].bank_account_id, self.bank.id)
        self.assertEqual(intents[0].amount, self.item1.amount)
        self.assertEqual(intents[0].subject_id, 'student_id')
        self.assertEqual(intents[0].school_year, '2023/2024')
        self.assertEqual(intents[0].payment_object_id, self.item1.id)
        self.assertEqual(intents[0].payment_object_type, 'movement')

        self.assertEqual(intents[1].bank_account_id, self.bank.id)
        self.assertEqual(intents[1].amount, 20.20)
        self.assertEqual(intents[1].subject_id, 'student_id')
        self.assertEqual(intents[1].school_year, '2023/2024')
        self.assertEqual(intents[1].payment_object_id, self.item2.id)
        self.assertEqual(intents[1].payment_object_type, 'movement_type')


    def test_crea_payment_intents_qty(self):
        articoli = [
            {
                'tipo': 'movimento',
                'id': self.item1.id,
                'amount': 10.10,
                'qta': 1
            },
            {
                'tipo': 'tipo_movimento',
                'id': self.item2.id,
                'amount': 20.20,
                'qta': 2
            },
            {
                'tipo': 'tipo_movimento',
                'id': self.item3.id,
                'amount': 20.20,
                'qta': 3
            }

        ]

        intents = crea_payment_intents(articoli, 'test_user', 'student_id', '2023_2024', banca=self.bank, request=self.request)
        self.assertEqual(len(intents), 6)
        self.assertEqual(intents[0].bank_account_id, self.bank.id)
        self.assertEqual(intents[0].amount, self.item1.amount)
        self.assertEqual(intents[0].subject_id, 'student_id')
        self.assertEqual(intents[0].school_year, '2023/2024')
        self.assertEqual(intents[0].payment_object_id, self.item1.id)
        self.assertEqual(intents[0].payment_object_type, 'movement')

        self.assertEqual(intents[1].bank_account_id, self.bank.id)
        self.assertEqual(intents[1].amount, 20.20)
        self.assertEqual(intents[1].subject_id, 'student_id')
        self.assertEqual(intents[1].school_year, '2023/2024')
        self.assertEqual(intents[1].payment_object_id, self.item2.id)
        self.assertEqual(intents[1].payment_object_type, 'movement_type')


        self.assertEqual(intents[5].bank_account_id, self.bank.id)
        self.assertEqual(intents[5].amount, 30.30)
        self.assertEqual(intents[5].subject_id, 'student_id')
        self.assertEqual(intents[5].school_year, '2023/2024')
        self.assertEqual(intents[5].payment_object_id, self.item3.id)
        self.assertEqual(intents[5].payment_object_type, 'movement_type')