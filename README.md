# MasterCom2


### Steps done

1. Inspect db from a local updated database.
2. Fix related_name where is needed

## For existing schools

Use migrate --fake-initial for existing school

## For new schools

TODO: Raw sql for triggers in audit if database is postgres. For local sqlite db, skip them (audit will be not tested)

## In local

1. Execute audit_schema.sql (mc2api) into database
2. Run fake initial migration. `./manage.py migrate --settings=settings.dev_postgres --fake-initial`
3. Add one superuser `./manage.py createsuperuser --settings=settings.dev_postgres`
4. Run django. `./manage.py runserver --settings=settings.dev_postgres`

## Tests

### Local
    ./manage.py test

### Coverage
    coverage run manage.py test && coverage report --skip-covered -m
    coverage run manage.py test && coverage html && open file://${PWD}/htmlcov/index.html

### Docker
    docker compose build && docker compose run --rm django /src/docker/coverage.sh
