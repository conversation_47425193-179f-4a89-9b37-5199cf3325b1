# This is an auto-generated Django model module.
# You'll have to do the following manually to clean this up:
#   * Rearrange models' order
#   * Make sure each model has one field with primary_key=True
#   * Make sure each ForeignKey and OneToOneField has `on_delete` set to the desired behavior
#   * Remove `managed = False` lines if you wish to allow Django to create, modify, and delete the table
# Feel free to rename the models, but don't rename db_table values or field names.
from django.db import models


class Abi(models.Model):
    id = models.AutoField()
    code = models.CharField(max_length=5, blank=True, null=True)
    descr = models.TextField(blank=True, null=True)
    date_act = models.IntegerField(blank=True, null=True)
    substitute = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'abi'


class AbsKind(models.Model):
    code = models.CharField(max_length=10)
    description = models.CharField(max_length=255)
    date_start = models.DateTimeField()
    date_end = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'abs_kind'


class AbsenceKind(models.Model):
    code = models.CharField(primary_key=True, max_length=10)
    description = models.TextField()
    absence_stack = models.ForeignKey('AbsenceStack', models.DO_NOTHING, db_column='absence_stack', blank=True, null=True)
    date_start = models.DateTimeField(blank=True, null=True)
    date_end = models.DateTimeField(blank=True, null=True)
    calc_festivities = models.BooleanField()
    calc_ferials = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'absence_kind'


class AbsenceKindSteps(models.Model):
    absence_steps_id = models.AutoField(primary_key=True)
    code = models.TextField()
    days = models.BigIntegerField()
    percentage = models.FloatField()
    role = models.SmallIntegerField()
    date = models.BigIntegerField()
    period = models.BigIntegerField()
    kind = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'absence_kind_steps'


class AbsenceStack(models.Model):
    unit = models.CharField(max_length=1)
    denomination = models.CharField(max_length=255)
    recover = models.BooleanField()
    reset_type = models.SmallIntegerField()
    reset_date = models.DateTimeField(blank=True, null=True)
    reset_to_stack = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True)
    reset_default_quota = models.FloatField()

    class Meta:
        managed = False
        db_table = 'absence_stack'


class Absences(models.Model):
    absence_id = models.BigAutoField(primary_key=True)
    start_date = models.BigIntegerField(blank=True, null=True)
    end_date = models.BigIntegerField(blank=True, null=True)
    ab_kind = models.CharField(max_length=10, blank=True, null=True)
    total_days = models.IntegerField(blank=True, null=True)
    employee = models.ForeignKey('Employee', models.DO_NOTHING, blank=True, null=True)
    date_of_req = models.BigIntegerField(blank=True, null=True)
    protocol_id = models.BigIntegerField(blank=True, null=True)
    type_of_abs = models.SmallIntegerField(blank=True, null=True)
    decreto = models.IntegerField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'absences'


class ActivityType(models.Model):
    description = models.CharField(max_length=100)
    code = models.CharField(primary_key=True, max_length=5)

    class Meta:
        managed = False
        db_table = 'activity_type'


class AlboArea(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'albo_area'


class AlboCategory(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    duration = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'albo_category'


class AlboEntity(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'albo_entity'


class AlboPublication(models.Model):
    number = models.IntegerField(blank=True, null=True)
    title = models.TextField()
    description = models.TextField(blank=True, null=True)
    start_date = models.BigIntegerField()
    expiration_date = models.BigIntegerField()
    extended_expiration_date = models.BigIntegerField(blank=True, null=True)
    publication_date = models.BigIntegerField(blank=True, null=True)
    extension_date = models.BigIntegerField(blank=True, null=True)
    cancelation_date = models.BigIntegerField(blank=True, null=True)
    category = models.ForeignKey(AlboCategory, models.DO_NOTHING)
    entity = models.ForeignKey(AlboEntity, models.DO_NOTHING)
    area = models.ForeignKey(AlboArea, models.DO_NOTHING)
    omissis = models.BooleanField()
    internal = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'albo_publication'


class AlboPublicationDocument(models.Model):
    publication = models.OneToOneField(AlboPublication, models.DO_NOTHING, primary_key=True)
    document = models.ForeignKey('ArchiveDocumentFile', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'albo_publication_document'
        unique_together = (('publication', 'document'),)


class AlboPublicationHistory(models.Model):
    action = models.CharField(max_length=1)
    date = models.BigIntegerField()
    publication = models.ForeignKey(AlboPublication, models.DO_NOTHING)
    user = models.ForeignKey('Users', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'albo_publication_history'


class AppType(models.Model):
    description = models.CharField(max_length=50)
    code = models.CharField(primary_key=True, max_length=5)

    class Meta:
        managed = False
        db_table = 'app_type'


class Appointment(models.Model):
    appointment_id = models.AutoField(primary_key=True)
    juridical_exp = models.BigIntegerField(blank=True, null=True)
    economical_exp = models.BigIntegerField(blank=True, null=True)
    app_kind = models.CharField(max_length=20, blank=True, null=True)
    pos_kind = models.CharField(max_length=3, blank=True, null=True)
    pers_kind = models.CharField(max_length=10, blank=True, null=True)
    contest_type = models.CharField(max_length=4, blank=True, null=True)
    service_seat = models.CharField(max_length=50, blank=True, null=True)
    auth_type = models.CharField(max_length=50, blank=True, null=True)
    payment_class = models.CharField(max_length=3, blank=True, null=True)
    direction_type = models.CharField(max_length=2, blank=True, null=True)
    contract_protocol = models.BigIntegerField(blank=True, null=True)
    short_term_id = models.IntegerField(blank=True, null=True)
    long_term_id = models.IntegerField(blank=True, null=True)
    teacher_class = models.CharField(max_length=20, blank=True, null=True)
    retr_categ = models.CharField(max_length=5, blank=True, null=True)
    economical_start = models.BigIntegerField(blank=True, null=True)
    institute_id = models.IntegerField(blank=True, null=True)
    employee_id = models.IntegerField(blank=True, null=True)
    cat_of_payment = models.CharField(max_length=1, blank=True, null=True)
    juridical_start = models.BigIntegerField(blank=True, null=True)
    worktime = models.TextField()
    flag_canc = models.BooleanField(blank=True, null=True)
    nomination_type = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'appointment'


class ArchiveCheckDocument(models.Model):
    archive_document = models.OneToOneField('ArchiveDocument', models.DO_NOTHING, db_column='archive_document', primary_key=True)
    user = models.ForeignKey('Users', models.DO_NOTHING, db_column='user')
    checked = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_check_document'
        unique_together = (('archive_document', 'user'),)


class ArchiveClass(models.Model):
    name = models.CharField(max_length=30)
    code = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    format = models.CharField(max_length=5)
    action = models.CharField(max_length=1)
    editable = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'archive_class'


class ArchiveClassStep(models.Model):
    archive_class = models.ForeignKey(ArchiveClass, models.DO_NOTHING, db_column='archive_class')
    sort = models.IntegerField()
    user_id = models.IntegerField(blank=True, null=True)
    protocol = models.BooleanField()
    albo = models.BooleanField()
    trasparenza = models.BooleanField()
    sign = models.BooleanField()
    archive = models.BooleanField(blank=True, null=True)
    type = models.CharField(max_length=1, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_class_step'


class ArchiveDocument(models.Model):
    short_description = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    upload_date = models.BigIntegerField()
    user = models.ForeignKey('Users', models.DO_NOTHING, blank=True, null=True)
    archive_user = models.ForeignKey('ArchiveUser', models.DO_NOTHING, blank=True, null=True)
    metadata = models.TextField()
    class_field = models.ForeignKey(ArchiveClass, models.DO_NOTHING, db_column='class_id', blank=True, null=True)  # Field renamed because it was a Python reserved word.
    action_archive_date = models.BigIntegerField(blank=True, null=True)
    action_protocol = models.BooleanField()
    action_protocol_date = models.BigIntegerField(blank=True, null=True)
    action_albo = models.BooleanField()
    action_albo_date = models.BigIntegerField(blank=True, null=True)
    action_trasparenza = models.BooleanField()
    action_trasparenza_date = models.BigIntegerField(blank=True, null=True)
    conserved = models.BooleanField()
    origin = models.ForeignKey('ArchiveOrigin', models.DO_NOTHING)
    action_sign = models.BooleanField()
    action_sign_date = models.BigIntegerField(blank=True, null=True)
    class_name = models.CharField(max_length=30, blank=True, null=True)
    action_archive = models.BooleanField(blank=True, null=True)
    remote_class_code = models.CharField(max_length=64, blank=True, null=True)
    assign_to_office = models.ForeignKey('ArchiveOffice', models.DO_NOTHING, db_column='assign_to_office', blank=True, null=True)
    assign_to_user = models.ForeignKey('Users', models.DO_NOTHING, db_column='assign_to_user', blank=True, null=True)
    assign_from_user = models.ForeignKey('Users', models.DO_NOTHING, db_column='assign_from_user', blank=True, null=True)
    completed = models.DateTimeField(blank=True, null=True)
    model = models.ForeignKey('ArchiveDocumentModel', models.DO_NOTHING, db_column='model', blank=True, null=True)
    expiration_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_document'


class ArchiveDocumentDossier(models.Model):
    archive_document = models.ForeignKey(ArchiveDocument, models.DO_NOTHING, db_column='archive_document')
    dossier = models.ForeignKey('Dossier', models.DO_NOTHING, db_column='dossier')

    class Meta:
        managed = False
        db_table = 'archive_document_dossier'


class ArchiveDocumentFile(models.Model):
    archive_document = models.ForeignKey(ArchiveDocument, models.DO_NOTHING, db_column='archive_document', blank=True, null=True)
    filename = models.TextField()
    filetype = models.TextField()
    path = models.CharField(max_length=255, blank=True, null=True)
    size = models.IntegerField(blank=True, null=True)
    token = models.CharField(max_length=255, blank=True, null=True)
    metadata = models.TextField(blank=True, null=True)
    external_data = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_document_file'


class ArchiveDocumentModel(models.Model):
    name = models.CharField(max_length=255)
    template = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_document_model'


class ArchiveDocumentStep(models.Model):
    archive_document = models.ForeignKey(ArchiveDocument, models.DO_NOTHING, db_column='archive_document')
    sort = models.IntegerField()
    user_id = models.IntegerField(blank=True, null=True)
    protocol = models.BooleanField()
    albo = models.BooleanField()
    trasparenza = models.BooleanField()
    sign = models.BooleanField()
    archive = models.BooleanField(blank=True, null=True)
    type = models.CharField(max_length=1, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_document_step'


class ArchiveLog(models.Model):
    date = models.DateTimeField()
    user = models.IntegerField()
    document = models.IntegerField()
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_log'


class ArchiveMail(models.Model):
    id = models.CharField(primary_key=True, max_length=32)
    account = models.ForeignKey('ArchiveMailAccount', models.DO_NOTHING, db_column='account')
    date = models.DateTimeField()
    from_field = models.TextField(db_column='from')  # Field renamed because it was a Python reserved word.
    to = models.TextField()
    cc = models.TextField(blank=True, null=True)
    ccn = models.TextField(blank=True, null=True)
    subject = models.TextField(blank=True, null=True)
    message = models.TextField(blank=True, null=True)
    raw = models.TextField(blank=True, null=True)
    deleted = models.BooleanField()
    assign_to_office = models.ForeignKey('ArchiveOffice', models.DO_NOTHING, db_column='assign_to_office', blank=True, null=True)
    assign_to_user = models.ForeignKey('Users', models.DO_NOTHING, db_column='assign_to_user', blank=True, null=True)
    assign_from_user = models.ForeignKey('Users', models.DO_NOTHING, db_column='assign_from_user', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_mail'
        unique_together = (('id', 'account'),)


class ArchiveMailAccount(models.Model):
    name = models.CharField(max_length=255)
    protocol = models.ForeignKey('ArchiveMailProtocol', models.DO_NOTHING, db_column='protocol')
    host = models.CharField(max_length=255)
    port = models.IntegerField()
    security = models.ForeignKey('ArchiveMailSecurity', models.DO_NOTHING, db_column='security')
    username = models.CharField(max_length=255)
    password = models.CharField(max_length=255)
    active = models.BooleanField()
    out = models.BooleanField()
    outname = models.CharField(max_length=255, blank=True, null=True)
    fatturapa = models.BooleanField()
    connection_class = models.CharField(max_length=255, blank=True, null=True)
    reminder = models.BooleanField()
    imap_encoding = models.CharField(max_length=50)
    params = models.TextField(blank=True, null=True)
    authentication = models.TextField()
    vendor = models.TextField()

    class Meta:
        managed = False
        db_table = 'archive_mail_account'


class ArchiveMailAttachment(models.Model):
    mail = models.ForeignKey(ArchiveMail, models.DO_NOTHING, db_column='mail')
    account = models.IntegerField()
    name = models.CharField(max_length=255)
    path = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'archive_mail_attachment'


class ArchiveMailDocument(models.Model):
    mail = models.ForeignKey(ArchiveMail, models.DO_NOTHING, db_column='mail')
    account = models.IntegerField()
    document = models.ForeignKey(ArchiveDocument, models.DO_NOTHING, db_column='document')
    sent = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_mail_document'


class ArchiveMailProtocol(models.Model):
    name = models.CharField(max_length=8)

    class Meta:
        managed = False
        db_table = 'archive_mail_protocol'


class ArchiveMailSecurity(models.Model):
    name = models.CharField(max_length=20)

    class Meta:
        managed = False
        db_table = 'archive_mail_security'


class ArchiveMetadata(models.Model):
    name = models.CharField(max_length=30)
    slug = models.CharField(max_length=30)
    required = models.BooleanField()
    kind = models.CharField(max_length=1)
    remote_class = models.ForeignKey('ArchiveRemoteClass', models.DO_NOTHING, db_column='remote_class')

    class Meta:
        managed = False
        db_table = 'archive_metadata'


class ArchiveOffice(models.Model):
    name = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_office'


class ArchiveOfficeUser(models.Model):
    office = models.ForeignKey(ArchiveOffice, models.DO_NOTHING, db_column='office', blank=True, null=True)
    user = models.ForeignKey('Users', models.DO_NOTHING, db_column='user', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_office_user'


class ArchiveOrigin(models.Model):
    name = models.CharField(max_length=10)
    code = models.CharField(max_length=5)
    description = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'archive_origin'


class ArchivePrivilege(models.Model):
    user = models.ForeignKey('ArchiveUser', models.DO_NOTHING)
    class_field = models.ForeignKey(ArchiveClass, models.DO_NOTHING, db_column='class_id')  # Field renamed because it was a Python reserved word.
    view = models.BooleanField()
    upload = models.BooleanField()
    delete = models.BooleanField()
    resend = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'archive_privilege'


class ArchiveRemoteClass(models.Model):
    code = models.CharField(primary_key=True, max_length=64)
    name = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'archive_remote_class'


class ArchiveUser(models.Model):
    name = models.CharField(max_length=30)
    username = models.CharField(max_length=50)
    password = models.CharField(max_length=50, blank=True, null=True)
    alias = models.CharField(max_length=50, blank=True, null=True)
    pin = models.CharField(max_length=50, blank=True, null=True)
    active = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'archive_user'


class Asl(models.Model):
    code = models.CharField(primary_key=True, max_length=1)
    description = models.CharField(max_length=30)

    class Meta:
        managed = False
        db_table = 'asl'


class AtaProjectHourTypes(models.Model):
    ata_project_hour_type_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=255)
    price_per_hour = models.FloatField()
    irap_perc = models.FloatField(blank=True, null=True)
    inpdap_perc = models.FloatField(blank=True, null=True)
    inps_perc = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'ata_project_hour_types'


class AtaProjectHtypesProjects(models.Model):
    ata_project_htypes_projects_id = models.AutoField(primary_key=True)
    type_id = models.BigIntegerField()
    project_id = models.BigIntegerField()
    dedicated_time = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'ata_project_htypes_projects'


class AtecoCode(models.Model):
    id = models.IntegerField(primary_key=True)
    code_ateco = models.TextField()
    description = models.TextField()

    class Meta:
        managed = False
        db_table = 'ateco_code'


class AuditAbsenceKind(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    code = models.CharField(max_length=10)
    description = models.TextField()
    absence_stack = models.BigIntegerField(blank=True, null=True)
    date_start = models.DateTimeField(blank=True, null=True)
    date_end = models.DateTimeField(blank=True, null=True)
    calc_festivities = models.BooleanField()
    calc_ferials = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'audit_absence_kind'


class AuditAbsenceStack(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    unit = models.CharField(max_length=1)
    denomination = models.CharField(max_length=255)
    recover = models.BooleanField()
    reset_type = models.SmallIntegerField()
    reset_date = models.DateTimeField(blank=True, null=True)
    reset_to_stack_id = models.BigIntegerField(blank=True, null=True)
    reset_default_quota = models.FloatField()

    class Meta:
        managed = False
        db_table = 'audit_absence_stack'


class AuditAbsences(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    absence_id = models.BigIntegerField(blank=True, null=True)
    start_date = models.BigIntegerField(blank=True, null=True)
    end_date = models.BigIntegerField(blank=True, null=True)
    ab_kind = models.CharField(max_length=10, blank=True, null=True)
    total_days = models.IntegerField(blank=True, null=True)
    employee_id = models.IntegerField(blank=True, null=True)
    date_of_req = models.BigIntegerField(blank=True, null=True)
    protocol_id = models.BigIntegerField(blank=True, null=True)
    type_of_abs = models.SmallIntegerField(blank=True, null=True)
    decreto = models.IntegerField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_absences'


class AuditAlboArea(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_albo_area'


class AuditAlboCategory(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    duration = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'audit_albo_category'


class AuditAlboEntity(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_albo_entity'


class AuditAlboPublication(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    number = models.IntegerField(blank=True, null=True)
    title = models.TextField()
    description = models.TextField(blank=True, null=True)
    start_date = models.BigIntegerField()
    expiration_date = models.BigIntegerField()
    extended_expiration_date = models.BigIntegerField(blank=True, null=True)
    publication_date = models.BigIntegerField(blank=True, null=True)
    extension_date = models.BigIntegerField(blank=True, null=True)
    cancelation_date = models.BigIntegerField(blank=True, null=True)
    category_id = models.IntegerField()
    entity_id = models.IntegerField()
    area_id = models.IntegerField()
    omissis = models.BooleanField()
    internal = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'audit_albo_publication'


class AuditAlboPublicationDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    publication_id = models.IntegerField()
    document_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_albo_publication_document'


class AuditAlboPublicationHistory(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    action = models.CharField(max_length=1)
    date = models.BigIntegerField()
    publication_id = models.IntegerField()
    user_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_albo_publication_history'


class AuditArchiveCheckDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    archive_document = models.BigIntegerField()
    user = models.BigIntegerField()
    checked = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_check_document'


class AuditArchiveClass(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=30)
    code = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    format = models.CharField(max_length=5)
    action = models.CharField(max_length=1)
    editable = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_class'


class AuditArchiveClassStep(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    archive_class = models.IntegerField(blank=True, null=True)
    sort = models.IntegerField(blank=True, null=True)
    user_id = models.IntegerField(blank=True, null=True)
    protocol = models.BooleanField(blank=True, null=True)
    albo = models.BooleanField(blank=True, null=True)
    trasparenza = models.BooleanField(blank=True, null=True)
    sign = models.BooleanField(blank=True, null=True)
    archive = models.BooleanField(blank=True, null=True)
    type = models.CharField(max_length=1, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_class_step'


class AuditArchiveDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    short_description = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    upload_date = models.BigIntegerField()
    user_id = models.IntegerField(blank=True, null=True)
    archive_user_id = models.IntegerField(blank=True, null=True)
    metadata = models.TextField()
    class_id = models.IntegerField(blank=True, null=True)
    action_archive_date = models.BigIntegerField(blank=True, null=True)
    action_protocol = models.BooleanField()
    action_protocol_date = models.BigIntegerField(blank=True, null=True)
    action_albo = models.BooleanField()
    action_albo_date = models.BigIntegerField(blank=True, null=True)
    action_trasparenza = models.BooleanField()
    action_trasparenza_date = models.BigIntegerField(blank=True, null=True)
    conserved = models.BooleanField()
    origin_id = models.IntegerField()
    action_sign = models.BooleanField()
    action_sign_date = models.BigIntegerField(blank=True, null=True)
    class_name = models.CharField(max_length=30, blank=True, null=True)
    action_archive = models.BooleanField(blank=True, null=True)
    remote_class_code = models.CharField(max_length=64, blank=True, null=True)
    assign_to_office = models.IntegerField(blank=True, null=True)
    assign_to_user = models.IntegerField(blank=True, null=True)
    assign_from_user = models.IntegerField(blank=True, null=True)
    completed = models.DateTimeField(blank=True, null=True)
    model = models.IntegerField(blank=True, null=True)
    expiration_date = models.DateTimeField(blank=True, null=True)
    dossier = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_document'


class AuditArchiveDocumentFile(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    archive_document = models.IntegerField(blank=True, null=True)
    filename = models.TextField()
    filetype = models.TextField()
    path = models.CharField(max_length=255, blank=True, null=True)
    size = models.IntegerField(blank=True, null=True)
    token = models.CharField(max_length=255, blank=True, null=True)
    metadata = models.TextField(blank=True, null=True)
    external_data = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_document_file'


class AuditArchiveDocumentModel(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    template = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_document_model'


class AuditArchiveDocumentStep(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    archive_document = models.IntegerField(blank=True, null=True)
    sort = models.IntegerField(blank=True, null=True)
    user_id = models.IntegerField(blank=True, null=True)
    protocol = models.BooleanField(blank=True, null=True)
    albo = models.BooleanField(blank=True, null=True)
    trasparenza = models.BooleanField(blank=True, null=True)
    sign = models.BooleanField(blank=True, null=True)
    archive = models.BooleanField(blank=True, null=True)
    type = models.CharField(max_length=1, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_document_step'


class AuditArchiveMail(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.CharField(max_length=32, blank=True, null=True)
    account = models.IntegerField(blank=True, null=True)
    date = models.DateTimeField(blank=True, null=True)
    from_field = models.TextField(db_column='from', blank=True, null=True)  # Field renamed because it was a Python reserved word.
    to = models.TextField(blank=True, null=True)
    cc = models.TextField(blank=True, null=True)
    ccn = models.TextField(blank=True, null=True)
    subject = models.TextField(blank=True, null=True)
    message = models.TextField(blank=True, null=True)
    raw = models.TextField(blank=True, null=True)
    deleted = models.BooleanField(blank=True, null=True)
    assign_to_office = models.IntegerField(blank=True, null=True)
    assign_to_user = models.IntegerField(blank=True, null=True)
    assign_from_user = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_mail'


class AuditArchiveMailAccount(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    protocol = models.IntegerField(blank=True, null=True)
    host = models.CharField(max_length=255, blank=True, null=True)
    port = models.IntegerField(blank=True, null=True)
    security = models.IntegerField(blank=True, null=True)
    username = models.CharField(max_length=255, blank=True, null=True)
    password = models.CharField(max_length=255, blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    out = models.BooleanField()
    outname = models.CharField(max_length=255, blank=True, null=True)
    fatturapa = models.BooleanField(blank=True, null=True)
    connection_class = models.CharField(max_length=255, blank=True, null=True)
    reminder = models.BooleanField()
    imap_encoding = models.CharField(max_length=50)
    params = models.TextField(blank=True, null=True)
    authentication = models.TextField()
    vendor = models.TextField()

    class Meta:
        managed = False
        db_table = 'audit_archive_mail_account'


class AuditArchiveMailAttachment(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    mail = models.CharField(max_length=32, blank=True, null=True)
    account = models.IntegerField(blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    path = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_mail_attachment'


class AuditArchiveMailDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    mail = models.CharField(max_length=32)
    account = models.IntegerField()
    document = models.IntegerField()
    sent = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_mail_document'


class AuditArchiveMailProtocol(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=8, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_mail_protocol'


class AuditArchiveMailSecurity(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    name = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_mail_security'


class AuditArchiveOffice(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField()
    name = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_office'


class AuditArchiveOfficeUser(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField()
    office = models.BigIntegerField()
    user = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'audit_archive_office_user'


class AuditArchiveOrigin(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=10)
    code = models.CharField(max_length=5)
    description = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_archive_origin'


class AuditArchivePrivilege(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    user_id = models.IntegerField()
    class_id = models.IntegerField()
    view = models.BooleanField()
    upload = models.BooleanField()
    delete = models.BooleanField()
    resend = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'audit_archive_privilege'


class AuditArchiveUser(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=30)
    username = models.CharField(max_length=50)
    password = models.CharField(max_length=50, blank=True, null=True)
    alias = models.CharField(max_length=50, blank=True, null=True)
    pin = models.CharField(max_length=50, blank=True, null=True)
    active = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'audit_archive_user'


class AuditBdgActivities(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    activ_id = models.IntegerField()
    aggreg_code = models.CharField(max_length=1)
    aggreg_nr = models.SmallIntegerField()
    description = models.CharField(max_length=200)
    ext_desc = models.TextField(blank=True, null=True)
    start_date = models.BigIntegerField(blank=True, null=True)
    end_date = models.BigIntegerField(blank=True, null=True)
    suspend = models.BooleanField(blank=True, null=True)
    avm = models.BooleanField(blank=True, null=True)
    notsdate = models.BooleanField(blank=True, null=True)
    notedate = models.BooleanField(blank=True, null=True)
    responsibles = models.TextField(blank=True, null=True)
    objectives = models.TextField(blank=True, null=True)
    human_resources = models.TextField(blank=True, null=True)
    goods_services = models.TextField(blank=True, null=True)
    durata = models.TextField(blank=True, null=True)
    budget_year = models.IntegerField()
    residui = models.FloatField()
    hours_insertions_end_date = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_bdg_activities'


class AuditCcpAdditional(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    positive = models.BooleanField()
    percentual = models.BooleanField()
    payment = models.BooleanField()
    amount = models.FloatField(blank=True, null=True)
    code = models.CharField(max_length=20, blank=True, null=True)
    on_gross = models.BooleanField(blank=True, null=True)
    codice_conto = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_additional'


class AuditCcpAdditionalTemplates(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    additional_id = models.IntegerField(blank=True, null=True)
    type_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_additional_templates'


class AuditCcpCategory(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=50)
    initial_balance = models.FloatField()

    class Meta:
        managed = False
        db_table = 'audit_ccp_category'


class AuditCcpCategoryBanks(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    category_id = models.IntegerField(blank=True, null=True)
    bank_id = models.IntegerField(blank=True, null=True)
    initial_balance = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_category_banks'


class AuditCcpCredits(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    credit_type_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_ccp_credits'


class AuditCcpCreditsType(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    description = models.CharField(max_length=255, blank=True, null=True)
    is_default = models.BooleanField(blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    discount = models.BooleanField()
    dote = models.IntegerField()
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    movement = models.BooleanField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile = models.CharField(max_length=255, blank=True, null=True)
    causale_contabile_uscite = models.CharField(max_length=255, blank=True, null=True)
    ccp_payment_method = models.IntegerField(blank=True, null=True)
    show_on_site = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_credits_type'


class AuditCcpDepositSlip(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    number = models.IntegerField()
    date = models.DateTimeField(blank=True, null=True)
    bank_account = models.IntegerField(blank=True, null=True)
    bank_account_iban = models.CharField(max_length=255, blank=True, null=True)
    bank_account_name = models.CharField(max_length=255, blank=True, null=True)
    payment_method = models.IntegerField(blank=True, null=True)
    payment_method_name = models.CharField(max_length=255, blank=True, null=True)
    bank_account_cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.CharField(max_length=255, blank=True, null=True)
    school_year = models.CharField(max_length=9, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_deposit_slip'


class AuditCcpDeposits(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    operation_date = models.BigIntegerField(blank=True, null=True)
    accountable_date = models.BigIntegerField(blank=True, null=True)
    amount = models.FloatField(blank=True, null=True)
    credits_id = models.IntegerField()
    payment_method_id = models.IntegerField(blank=True, null=True)
    payer_type = models.CharField(max_length=1, blank=True, null=True)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_name = models.CharField(max_length=50, blank=True, null=True)
    payer_surname = models.CharField(max_length=50, blank=True, null=True)
    payer_fiscal_code = models.CharField(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=80, blank=True, null=True)
    payer_city = models.CharField(max_length=30, blank=True, null=True)
    payer_province = models.CharField(max_length=2, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=5, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    deposit_type = models.CharField(max_length=1, blank=True, null=True)
    tx_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_deposits'


class AuditCcpInvoice(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    number = models.IntegerField()
    date = models.DateTimeField()
    accountholder = models.TextField()
    rows = models.TextField()
    total = models.FloatField()
    bank = models.TextField(blank=True, null=True)
    expiration_date = models.DateTimeField(blank=True, null=True)
    incoming = models.BooleanField(blank=True, null=True)
    xml_name = models.CharField(max_length=255, blank=True, null=True)
    expiration_text = models.TextField(blank=True, null=True)
    table_text = models.TextField(blank=True, null=True)
    ds_name = models.CharField(max_length=255, blank=True, null=True)
    ds_id = models.CharField(max_length=255, blank=True, null=True)
    credit_note = models.BooleanField(blank=True, null=True)
    payment_method = models.IntegerField(blank=True, null=True)
    publication_path = models.CharField(max_length=255, blank=True, null=True)
    header = models.TextField(blank=True, null=True)
    suffix = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_invoice'


class AuditCcpInvoiceDepositSlip(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    ccp_invoice = models.IntegerField(blank=True, null=True)
    ccp_deposit_slip = models.IntegerField(blank=True, null=True)
    unpaid_date = models.DateTimeField(blank=True, null=True)
    unpaid_note = models.TextField(blank=True, null=True)
    row_number = models.IntegerField(blank=True, null=True)
    total = models.FloatField()
    collection_cost = models.FloatField()
    bollo = models.FloatField()
    movements_total = models.FloatField()
    iban = models.TextField(blank=True, null=True)
    codice_rid = models.TextField(blank=True, null=True)
    data_mandato_rid = models.TextField(blank=True, null=True)
    first_sepa = models.TextField(blank=True, null=True)
    surname = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)
    province = models.CharField(max_length=255, blank=True, null=True)
    zip_code = models.CharField(max_length=255, blank=True, null=True)
    fiscal_code = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_invoice_deposit_slip'


class AuditCcpMovement(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    type_id = models.IntegerField(blank=True, null=True)
    miscellaneous = models.TextField(blank=True, null=True)
    number = models.IntegerField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)
    school_year = models.CharField(max_length=255, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    subject_data = models.TextField(blank=True, null=True)
    subject_seat = models.IntegerField(blank=True, null=True)
    subject_class = models.CharField(max_length=64, blank=True, null=True)
    amount = models.FloatField()
    creation_date = models.BigIntegerField()
    subject_type = models.CharField(max_length=1)
    expiration_date = models.BigIntegerField(blank=True, null=True)
    tmp_number = models.TextField(blank=True, null=True)
    invoice_id = models.IntegerField(blank=True, null=True)
    subject_school_address_code = models.CharField(max_length=255, blank=True, null=True)
    subject_school_address = models.CharField(max_length=255, blank=True, null=True)
    invoice_code = models.CharField(max_length=255, blank=True, null=True)
    description = models.CharField(max_length=255)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)
    ccp_easy_select = models.IntegerField(blank=True, null=True)
    subject_school_year = models.CharField(max_length=255, blank=True, null=True)
    payment_intent_token = models.CharField(max_length=255, blank=True, null=True)
    payment_intent_id = models.IntegerField(blank=True, null=True)
    locked = models.BooleanField(blank=True, null=True)
    date_published = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_movement'


class AuditCcpMovementAdditional(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    movement_id = models.IntegerField()
    additional_id = models.IntegerField()
    amount = models.FloatField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    id = models.IntegerField(blank=True, null=True)
    abs_amount = models.FloatField()

    class Meta:
        managed = False
        db_table = 'audit_ccp_movement_additional'


class AuditCcpPayment(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    movement_id = models.IntegerField()
    operation_date = models.BigIntegerField()
    accountable_date = models.BigIntegerField()
    amount = models.FloatField()
    payment_method_id = models.IntegerField()
    bollettino = models.CharField(max_length=50, blank=True, null=True)
    account_id = models.IntegerField()
    account_reference = models.CharField(max_length=50, blank=True, null=True)
    payer_type = models.CharField(max_length=1)
    payer_id = models.CharField(max_length=30, blank=True, null=True)
    payer_name = models.CharField(max_length=50)
    payer_surname = models.CharField(max_length=50)
    payer_fiscal_code = models.CharField(max_length=20, blank=True, null=True)
    payer_address = models.CharField(max_length=255, blank=True, null=True)
    payer_city = models.CharField(max_length=255, blank=True, null=True)
    payer_province = models.CharField(max_length=2, blank=True, null=True)
    payer_zip_code = models.CharField(max_length=5, blank=True, null=True)
    receipt_id = models.IntegerField(blank=True, null=True)
    ccp_credit = models.IntegerField(blank=True, null=True)
    ccp_deposit_slip = models.IntegerField(blank=True, null=True)
    easy_import_protocol = models.IntegerField(blank=True, null=True)
    covered_movement_id = models.IntegerField(blank=True, null=True)
    payment_group = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_payment'


class AuditCcpPaymentAdditional(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    payment_id = models.IntegerField()
    additional_id = models.IntegerField()
    amount = models.FloatField()

    class Meta:
        managed = False
        db_table = 'audit_ccp_payment_additional'


class AuditCcpPaymentMethod(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=50)
    easy_code = models.IntegerField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    easy_by_bank = models.BooleanField(blank=True, null=True)
    easy_export_grouped = models.BooleanField(blank=True, null=True)
    massive_payment_group = models.BooleanField(blank=True, null=True)
    causale_contabile = models.CharField(max_length=50, blank=True, null=True)
    causale_contabile_uscite = models.CharField(max_length=50, blank=True, null=True)
    data_raggruppamento = models.CharField(max_length=255, blank=True, null=True)
    tipo_raggruppamento = models.CharField(max_length=255, blank=True, null=True)
    piano_conti_da_tipo = models.BooleanField(blank=True, null=True)
    piano_conti_uscite_da_tipo = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_payment_method'


class AuditCcpReceipt(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    number = models.IntegerField()
    date = models.BigIntegerField()
    receipt = models.BooleanField(blank=True, null=True)
    publication_path = models.CharField(max_length=255, blank=True, null=True)
    groupment = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_receipt'


class AuditCcpReminderSubjects(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    ccp_reminder_id = models.IntegerField(blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_reminder_subjects'


class AuditCcpType(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    amount = models.FloatField()
    governative = models.BooleanField()
    expiration_date = models.BigIntegerField(blank=True, null=True)
    cumulative = models.IntegerField()
    school_year = models.CharField(max_length=9)
    category_id = models.IntegerField()
    incoming = models.BooleanField()
    section = models.CharField(max_length=255, blank=True, null=True)
    online_payment = models.BooleanField()
    invoice_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=255, blank=True, null=True)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)
    include_vat = models.BooleanField(blank=True, null=True)
    vat = models.FloatField(blank=True, null=True)
    bollo = models.BooleanField()
    vat_code_id = models.IntegerField(blank=True, null=True)
    easy_description = models.CharField(max_length=20, blank=True, null=True)
    payment_mail = models.CharField(max_length=255, blank=True, null=True)
    online_payment_status = models.IntegerField(blank=True, null=True)
    exclude_corrispettivi = models.BooleanField(blank=True, null=True)
    ccp_credits_type = models.IntegerField(blank=True, null=True)
    centro_costo_ricavo = models.CharField(max_length=50, blank=True, null=True)
    id_importazione = models.CharField(max_length=50, blank=True, null=True)
    ccp_ae_category_id = models.IntegerField(blank=True, null=True)
    pubblica_pagati_online = models.BooleanField(blank=True, null=True)
    include_bollo = models.BooleanField(blank=True, null=True)
    easy_code_contropartita = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_type'


class AuditCcpTypeAdditional(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    type_id = models.IntegerField()
    additional_id = models.IntegerField()
    amount = models.FloatField()
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    subject_id = models.IntegerField(blank=True, null=True)
    id = models.IntegerField(blank=True, null=True)
    discount_order = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_type_additional'


class AuditCcpTypeStep(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    ccp_type = models.IntegerField()
    expiration = models.CharField(max_length=255, blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    description = models.CharField(max_length=255)
    da_ratei = models.DateTimeField(blank=True, null=True)
    a_ratei = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_type_step'


class AuditCcpVatCode(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    code = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField()
    exemption = models.BooleanField(blank=True, null=True)
    sdi_code = models.CharField(max_length=255, blank=True, null=True)
    easy_code = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_ccp_vat_code'


class AuditContact(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    contact_id = models.IntegerField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    phone_num = models.TextField(blank=True, null=True)
    fax = models.TextField(blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)
    email = models.TextField(blank=True, null=True)
    mobile = models.TextField(blank=True, null=True)
    web = models.TextField(blank=True, null=True)
    cap = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_contact'


class AuditCoreBankAccount(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    country_code = models.CharField(max_length=2, blank=True, null=True)
    check_code = models.CharField(max_length=2, blank=True, null=True)
    bban = models.CharField(max_length=100)
    denomination = models.CharField(max_length=100)
    initial_balance = models.FloatField()
    type = models.CharField(max_length=1)
    ise_id = models.IntegerField()
    ise_type = models.CharField(max_length=1)
    invoice_default = models.BooleanField(blank=True, null=True)
    online_payment_default = models.BooleanField()
    cuc = models.CharField(max_length=255, blank=True, null=True)
    creditor_identifier = models.CharField(max_length=255, blank=True, null=True)
    pvr_number = models.CharField(max_length=20)
    agency_name = models.CharField(max_length=50, blank=True, null=True)
    agency_city = models.CharField(max_length=50, blank=True, null=True)
    agency_zip_code = models.CharField(max_length=10, blank=True, null=True)
    qr_iban = models.CharField(max_length=27, blank=True, null=True)
    qr_bank_account = models.CharField(max_length=27, blank=True, null=True)
    collection_cost = models.FloatField()
    sepa_regex = models.CharField(max_length=255, blank=True, null=True)
    sepa_accent = models.BooleanField(blank=True, null=True)
    piano_conti = models.CharField(max_length=255, blank=True, null=True)
    customer_desc = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'audit_core_bank_account'


class AuditCoreContactType(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20)

    class Meta:
        managed = False
        db_table = 'audit_core_contact_type'


class AuditCoreContactgroups(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    user_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_core_contactgroups'


class AuditCoreContacts(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    name = models.CharField(max_length=255)
    email = models.CharField(max_length=255)
    contact_type_id = models.IntegerField()
    user_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_core_contacts'


class AuditCoreContactsContactgroups(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    contact_id = models.IntegerField()
    group_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_core_contacts_contactgroups'


class AuditCoreForcedAction(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField(blank=True, null=True)
    date = models.DateTimeField(blank=True, null=True)
    section = models.CharField(max_length=16, blank=True, null=True)
    code = models.CharField(max_length=32, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    table = models.CharField(max_length=32, blank=True, null=True)
    id_value = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_core_forced_action'


class AuditEmployee(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    employee_id = models.IntegerField()
    name = models.CharField(max_length=100)
    surname = models.CharField(max_length=50)
    gender = models.CharField(max_length=10, blank=True, null=True)
    birthdate = models.BigIntegerField(blank=True, null=True)
    fiscal_code = models.CharField(max_length=30, blank=True, null=True)
    residence_id = models.IntegerField(blank=True, null=True)
    address_id = models.IntegerField(blank=True, null=True)
    part_spesa = models.CharField(max_length=16, blank=True, null=True)
    bank = models.IntegerField(blank=True, null=True)
    liq_office = models.CharField(max_length=4, blank=True, null=True)
    inps = models.CharField(max_length=17, blank=True, null=True)
    insur_qual = models.CharField(max_length=1, blank=True, null=True)
    fore = models.BooleanField(blank=True, null=True)
    asl = models.CharField(max_length=10, blank=True, null=True)
    adm_code = models.CharField(max_length=16, blank=True, null=True)
    way_pay = models.CharField(max_length=2, blank=True, null=True)
    liquid_group = models.CharField(max_length=4, blank=True, null=True)
    contr_code = models.CharField(max_length=2, blank=True, null=True)
    contr_type = models.CharField(max_length=5, blank=True, null=True)
    contr_cat = models.SmallIntegerField(blank=True, null=True)
    ssp_frm_pmnt = models.SmallIntegerField(blank=True, null=True)
    personal_data = models.SmallIntegerField(blank=True, null=True)
    susp = models.BooleanField(blank=True, null=True)
    payment_group = models.IntegerField(blank=True, null=True)
    priv_ret_type = models.CharField(max_length=5, blank=True, null=True)
    social_position = models.SmallIntegerField(blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    statal_code = models.CharField(max_length=16, blank=True, null=True)
    fiscal_city_code = models.CharField(max_length=16, blank=True, null=True)
    birthplace = models.TextField(blank=True, null=True)
    income = models.BigIntegerField(blank=True, null=True)
    state_birth = models.CharField(max_length=5, blank=True, null=True)
    citizenship = models.CharField(max_length=5, blank=True, null=True)
    id_sissi = models.CharField(max_length=5, blank=True, null=True)
    dom_first_prev_year = models.IntegerField(blank=True, null=True)
    dom_last_prev_year = models.IntegerField(blank=True, null=True)
    dom_first_curr_year = models.IntegerField(blank=True, null=True)
    qualification = models.TextField(blank=True, null=True)
    liquid_office_id = models.IntegerField()
    badge_number = models.BigIntegerField(blank=True, null=True)
    tolerance_in = models.IntegerField()
    tolerance_out = models.IntegerField()
    flexibility = models.IntegerField()
    generic_tolerance = models.IntegerField()
    negative_round = models.IntegerField()
    recover_hours = models.IntegerField()
    max_extraordinary_in = models.IntegerField()
    max_extraordinary_out = models.IntegerField()
    min_extraordinary_in = models.IntegerField()
    min_extraordinary_out = models.IntegerField()
    step_out = models.IntegerField()
    step_in = models.IntegerField()
    max_break = models.IntegerField()
    max_cont_work = models.IntegerField()
    simplified_ata_settings = models.BooleanField()
    tolerance_in_und = models.IntegerField()
    tolerance_out_und = models.IntegerField()
    max_undefined_in = models.IntegerField()
    max_undefined_out = models.IntegerField()
    min_undefined_in = models.IntegerField()
    min_undefined_out = models.IntegerField()
    step_out_und = models.IntegerField()
    step_in_und = models.IntegerField()
    undefined_parameter_active = models.BooleanField()
    min_extraordinary_total = models.IntegerField()
    max_extraordinary_total = models.IntegerField()
    min_undefined_total = models.IntegerField()
    max_undefined_total = models.IntegerField()
    step_total_undefined = models.IntegerField()
    step_total_extraordinary = models.IntegerField()
    lunch_duration = models.IntegerField()
    lunch_deductible = models.BooleanField()
    service_deductible = models.BooleanField()
    min_undefined_lunch = models.IntegerField()
    min_extraordinary_lunch = models.IntegerField()
    max_undefined_lunch = models.IntegerField()
    max_extraordinary_lunch = models.IntegerField()
    step_lunch_undefined = models.IntegerField()
    step_lunch_extraordinary = models.IntegerField()
    break_after_max_work = models.IntegerField()
    unit_recover_hours = models.CharField(max_length=3)
    max_work = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_employee'


class AuditExtraordinaryStored(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    extraordinary_stored_id = models.IntegerField()
    employee_id = models.BigIntegerField()
    date = models.BigIntegerField()
    recover_hours = models.IntegerField()
    extraordinary = models.IntegerField()
    max_extraordinary = models.IntegerField()
    to_recover = models.IntegerField()
    to_pay = models.IntegerField()
    authorized = models.IntegerField()
    authorized_undefined = models.IntegerField()
    undefined = models.IntegerField()
    note = models.TextField()

    class Meta:
        managed = False
        db_table = 'audit_extraordinary_stored'


class AuditInvoice(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    fiscal_code = models.CharField(max_length=255, blank=True, null=True)
    vat_number = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)
    city_name = models.CharField(max_length=255, blank=True, null=True)
    city_province = models.CharField(max_length=255, blank=True, null=True)
    city_postal_code = models.IntegerField(blank=True, null=True)
    total = models.FloatField(blank=True, null=True)
    xml = models.TextField(blank=True, null=True)
    number = models.CharField(max_length=255, blank=True, null=True)
    date = models.DateTimeField(blank=True, null=True)
    archive_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_invoice'


class AuditInvoiceExpiration(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField(blank=True, null=True)
    invoice_id = models.IntegerField(blank=True, null=True)
    expiration = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_invoice_expiration'


class AuditPersonnelPresences(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    personnel_presence_id = models.IntegerField()
    employee_id = models.BigIntegerField()
    project_id = models.BigIntegerField(blank=True, null=True)
    project_edit_id = models.BigIntegerField(blank=True, null=True)
    date = models.BigIntegerField(blank=True, null=True)
    date_edit = models.BigIntegerField(blank=True, null=True)
    type = models.IntegerField()
    type_edit = models.IntegerField()
    original_inout = models.IntegerField()
    original_inout_edit = models.IntegerField()
    description = models.CharField(max_length=2000)
    hour_type_id = models.BigIntegerField()
    hour_type_edit_id = models.BigIntegerField()
    insertion_mode = models.CharField(max_length=1)

    class Meta:
        managed = False
        db_table = 'audit_personnel_presences'


class AuditPersonnelProjects(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    personnel_projects_id = models.IntegerField()
    project_id = models.BigIntegerField(blank=True, null=True)
    personnel_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_personnel_projects'


class AuditPersonnelStacks(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField()
    employee_id = models.BigIntegerField()
    stack_id = models.BigIntegerField()
    reset_quota = models.FloatField()

    class Meta:
        managed = False
        db_table = 'audit_personnel_stacks'


class AuditPersonnelTimetable(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    personnel_timetable_id = models.IntegerField()
    employee_id = models.BigIntegerField()
    date_start = models.BigIntegerField()
    date_end = models.BigIntegerField()
    date_start_pause = models.BigIntegerField(blank=True, null=True)
    date_end_pause = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_personnel_timetable'


class AuditProtocolAction(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    description = models.CharField(max_length=100)
    comment = models.CharField(max_length=255, blank=True, null=True)
    active = models.BooleanField()
    type_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_protocol_action'


class AuditProtocolCorrespondent(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    title = models.TextField()
    note = models.TextField(blank=True, null=True)
    person_type = models.TextField(blank=True, null=True)
    correspondent_type_id = models.BigIntegerField(blank=True, null=True)
    correspondent_type = models.TextField()
    fiscal_code = models.CharField(max_length=50, blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)
    zipcode = models.CharField(max_length=5, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=64, blank=True, null=True)
    fax = models.CharField(max_length=64, blank=True, null=True)
    mobile = models.CharField(max_length=64, blank=True, null=True)
    email = models.CharField(max_length=64, blank=True, null=True)
    web = models.CharField(max_length=128, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_protocol_correspondent'


class AuditProtocolProtocol(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField()
    date = models.BigIntegerField()
    note = models.TextField(blank=True, null=True)
    budget_id = models.SmallIntegerField(blank=True, null=True)
    type_id = models.IntegerField(blank=True, null=True)
    rec_id = models.IntegerField(blank=True, null=True)
    rec_type = models.SmallIntegerField(blank=True, null=True)
    protocol_number = models.IntegerField()
    obj_id = models.IntegerField(blank=True, null=True)
    direction = models.CharField(max_length=1, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    correspondent_text = models.TextField(blank=True, null=True)
    canceled = models.BooleanField()
    external_act_number = models.TextField(blank=True, null=True)
    send_method_id = models.BigIntegerField(blank=True, null=True)
    dossier = models.TextField(blank=True, null=True)
    subject_kind_id = models.IntegerField(blank=True, null=True)
    reserved = models.BooleanField()
    header_position = models.CharField(max_length=255, blank=True, null=True)
    mail_sending = models.DateTimeField(blank=True, null=True)
    mail_error = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_protocol_protocol'


class AuditProtocolProtocolCorrespondent(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    protocol_id = models.BigIntegerField()
    correspondent_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'audit_protocol_protocol_correspondent'


class AuditProtocolProtocolDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    protocol_id = models.IntegerField()
    document_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_protocol_protocol_document'


class AuditProtocolProtocolHistory(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    action = models.CharField(max_length=1)
    date = models.BigIntegerField()
    protocol_id = models.IntegerField()
    user_id = models.IntegerField()
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_protocol_protocol_history'


class AuditProtocolProtocolProtocol(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    protocol_1_id = models.IntegerField()
    protocol_2_id = models.IntegerField()
    parent_relationship = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'audit_protocol_protocol_protocol'


class AuditProtocolSendMethod(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    title = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'audit_protocol_send_method'


class AuditProtocolSubjectKind(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    title = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'audit_protocol_subject_kind'


class AuditProtocolType(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    code = models.CharField(max_length=20)
    description = models.CharField(max_length=255, blank=True, null=True)
    parent_type_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_protocol_type'


class AuditReport(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.BigIntegerField()
    date_created = models.DateTimeField()
    print_key = models.CharField(max_length=255)
    print_type = models.CharField(max_length=255)
    subject_id = models.IntegerField(blank=True, null=True)
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    school_year = models.CharField(max_length=255, blank=True, null=True)
    template_id = models.IntegerField(blank=True, null=True)
    subject_data = models.CharField(max_length=255, blank=True, null=True)
    subject_class = models.CharField(max_length=100, blank=True, null=True)
    subject_school_address_code = models.CharField(max_length=20, blank=True, null=True)
    published = models.BooleanField(blank=True, null=True)
    status = models.CharField(max_length=20, blank=True, null=True)
    print_params = models.TextField(blank=True, null=True)
    last_result = models.TextField(blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    filepath = models.CharField(max_length=255, blank=True, null=True)
    report_key = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_report'


class AuditStoragePersonnelPresences(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    storage_personnel_presences_id = models.IntegerField()
    employee_id = models.IntegerField()
    date_start = models.BigIntegerField()
    date_end = models.BigIntegerField()
    extraordinary_authorized = models.IntegerField()
    permission_remain = models.IntegerField()
    vacation_remain = models.IntegerField()
    to_define = models.BigIntegerField()
    period_vacation = models.BigIntegerField()
    period_permission = models.BigIntegerField()
    ext_start_o = models.IntegerField()
    ext_start = models.IntegerField()
    ext_end_o = models.IntegerField()
    ext_end = models.IntegerField()
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_storage_personnel_presences'


class AuditStoragePersonnelStack(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    storage_personnel_presences = models.BigIntegerField(blank=True, null=True)
    absence_stack = models.BigIntegerField(blank=True, null=True)
    stack_denomination = models.CharField(max_length=255)
    value_start_o = models.FloatField()
    value_start = models.FloatField()
    value_end_o = models.FloatField()
    value_end = models.FloatField()
    unit = models.CharField(max_length=1)
    recover = models.BooleanField()
    reset_type_applied = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'audit_storage_personnel_stack'


class AuditSupplier(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    fiscal_code = models.CharField(max_length=255, blank=True, null=True)
    vat_number = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)
    city_name = models.CharField(max_length=255, blank=True, null=True)
    city_province = models.CharField(max_length=255, blank=True, null=True)
    city_postal_code = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_supplier'


class AuditTaxResiduals(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id_residual = models.IntegerField()
    year = models.IntegerField()
    tasse = models.FloatField(blank=True, null=True)
    contributi = models.FloatField(blank=True, null=True)
    quote = models.FloatField(blank=True, null=True)
    diversi = models.FloatField(blank=True, null=True)
    debito = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'audit_tax_residuals'


class AuditTrasparenzaVoice(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    id = models.IntegerField()
    title = models.CharField(max_length=255)
    content = models.TextField(blank=True, null=True)
    reference = models.TextField(blank=True, null=True)
    parent_voice_id = models.IntegerField(blank=True, null=True)
    published = models.BooleanField()
    last_update = models.BigIntegerField(blank=True, null=True)
    index = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_trasparenza_voice'


class AuditTrasparenzaVoiceDocument(models.Model):
    op_id = models.AutoField(primary_key=True)
    op_action = models.CharField(max_length=1)
    op_date = models.DateTimeField()
    voice_id = models.IntegerField()
    document_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'audit_trasparenza_voice_document'


class AuthElement(models.Model):
    name = models.CharField(max_length=50)
    control_interface = models.CharField(max_length=50)
    auth_permission = models.ForeignKey('AuthPermissions', models.DO_NOTHING, db_column='auth_permission', blank=True, null=True)
    state = models.CharField(max_length=10)

    class Meta:
        managed = False
        db_table = 'auth_element'


class AuthPath(models.Model):
    path = models.CharField(max_length=50)
    auth_permission = models.ForeignKey('AuthPermissions', models.DO_NOTHING, db_column='auth_permission', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'auth_path'


class AuthPermissionGroup(models.Model):
    groups = models.ForeignKey('Groups', models.DO_NOTHING, db_column='groups', blank=True, null=True)
    auth_permission = models.ForeignKey('AuthPermissions', models.DO_NOTHING, db_column='auth_permission', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'auth_permission_group'


class AuthPermissions(models.Model):
    title = models.CharField(max_length=255)
    super_user = models.BooleanField(blank=True, null=True)
    auth_section = models.ForeignKey('AuthSection', models.DO_NOTHING, db_column='auth_section', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'auth_permissions'


class AuthSection(models.Model):
    title = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'auth_section'


class AuthorityType(models.Model):
    authority_type_id = models.AutoField(primary_key=True)
    description = models.TextField()

    class Meta:
        managed = False
        db_table = 'authority_type'


class BankAcc(models.Model):
    account = models.CharField(max_length=20)
    cin = models.CharField(max_length=1, blank=True, null=True)
    bank_id = models.AutoField(primary_key=True)
    institute_id = models.IntegerField(blank=True, null=True)
    abi = models.IntegerField()
    cab = models.IntegerField()
    iban = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bank_acc'


class BankAccount(models.Model):
    account_id = models.AutoField(primary_key=True)
    account = models.TextField(blank=True, null=True)
    cin = models.CharField(max_length=1, blank=True, null=True)
    institute_id = models.BigIntegerField(blank=True, null=True)
    abi = models.IntegerField(blank=True, null=True)
    cab = models.IntegerField(blank=True, null=True)
    iban = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bank_account'


class BdgActSum(models.Model):
    act_sum_id = models.AutoField(primary_key=True)
    act_id = models.IntegerField()
    budget_id = models.SmallIntegerField()
    a_sum = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_act_sum'


class BdgActivities(models.Model):
    activ_id = models.AutoField(primary_key=True)
    aggreg_code = models.CharField(max_length=1)
    aggreg_nr = models.SmallIntegerField()
    description = models.CharField(max_length=200)
    ext_desc = models.TextField(blank=True, null=True)
    start_date = models.BigIntegerField(blank=True, null=True)
    end_date = models.BigIntegerField(blank=True, null=True)
    suspend = models.BooleanField(blank=True, null=True)
    avm = models.BooleanField(blank=True, null=True)
    notsdate = models.BooleanField(blank=True, null=True)
    notedate = models.BooleanField(blank=True, null=True)
    responsibles = models.TextField(blank=True, null=True)
    objectives = models.TextField(blank=True, null=True)
    human_resources = models.TextField(blank=True, null=True)
    goods_services = models.TextField(blank=True, null=True)
    durata = models.TextField(blank=True, null=True)
    budget_year = models.IntegerField()
    residui = models.FloatField()
    hours_insertions_end_date = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_activities'


class BdgAsses(models.Model):
    asses_id = models.AutoField(primary_key=True)
    number = models.IntegerField()
    date = models.BigIntegerField()
    voice_id = models.IntegerField()
    descr = models.TextField()
    del_num = models.CharField(max_length=20, blank=True, null=True)
    del_date = models.BigIntegerField(blank=True, null=True)
    tot_sum = models.FloatField()
    paid = models.SmallIntegerField(blank=True, null=True)
    budget_id = models.SmallIntegerField(blank=True, null=True)
    debitor = models.IntegerField()
    deb_type = models.SmallIntegerField()
    act_id = models.IntegerField(blank=True, null=True)
    tax_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'bdg_asses'


class BdgAssesLog(models.Model):
    asses_id = models.IntegerField()
    voice_in = models.IntegerField()
    voice_out = models.IntegerField()
    activity_id = models.IntegerField()
    summa = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_asses_log'


class BdgBaseActivities(models.Model):
    activ_id = models.AutoField(primary_key=True)
    aggreg_code = models.CharField(max_length=1)
    aggreg_nr = models.SmallIntegerField()
    description = models.CharField(max_length=200)
    ext_desc = models.TextField(blank=True, null=True)
    start_date = models.BigIntegerField(blank=True, null=True)
    end_date = models.BigIntegerField(blank=True, null=True)
    suspend = models.BooleanField(blank=True, null=True)
    avm = models.BooleanField(blank=True, null=True)
    notsdate = models.BooleanField(blank=True, null=True)
    notedate = models.BooleanField(blank=True, null=True)
    responsibles = models.TextField(blank=True, null=True)
    objectives = models.TextField(blank=True, null=True)
    human_resources = models.TextField(blank=True, null=True)
    goods_services = models.TextField(blank=True, null=True)
    durata = models.TextField(blank=True, null=True)
    budget_year = models.IntegerField()
    residui = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_base_activities'


class BdgBaseVoices(models.Model):
    voice_id = models.AutoField(primary_key=True)
    aggreg_nr = models.SmallIntegerField()
    voice_nr = models.SmallIntegerField(blank=True, null=True)
    description = models.CharField(max_length=200)
    ext_desc = models.TextField(blank=True, null=True)
    subvoice_nr = models.SmallIntegerField(blank=True, null=True)
    inout = models.SmallIntegerField(blank=True, null=True)
    blocked = models.BooleanField(blank=True, null=True)
    statal = models.BooleanField(blank=True, null=True)
    year = models.IntegerField()
    sum = models.FloatField()
    residui = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_base_voices'


class BdgBudgAct(models.Model):
    rel_ba_id = models.AutoField(primary_key=True)
    budget_id = models.IntegerField()
    activity_id = models.IntegerField(blank=True, null=True)
    sum = models.FloatField(blank=True, null=True)
    voice_id = models.IntegerField(blank=True, null=True)
    inout = models.SmallIntegerField(blank=True, null=True)
    voice_out_id = models.IntegerField(blank=True, null=True)
    sum_have = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_budg_act'


class BdgBudgVoice(models.Model):
    rel_bv_id = models.AutoField(primary_key=True)
    budget_id = models.IntegerField()
    voice_id = models.SmallIntegerField()
    sum = models.FloatField(blank=True, null=True)
    inout = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_budg_voice'


class BdgBudget(models.Model):
    budget_id = models.AutoField(primary_key=True)
    year = models.IntegerField()
    conf_date = models.BigIntegerField(blank=True, null=True)
    total_inc = models.FloatField(blank=True, null=True)
    total_exp = models.FloatField(blank=True, null=True)
    pred = models.BigIntegerField(blank=True, null=True)
    prop = models.BigIntegerField(blank=True, null=True)
    conf_num = models.CharField(max_length=10, blank=True, null=True)
    revised = models.BooleanField(blank=True, null=True)
    rev_date = models.BigIntegerField(blank=True, null=True)
    rev_num = models.CharField(max_length=10, blank=True, null=True)
    conf = models.BooleanField()
    minor = models.IntegerField(blank=True, null=True)
    init = models.IntegerField(blank=True, null=True)
    rev_opinion = models.IntegerField(blank=True, null=True)
    rev_opinion_date = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_budget'


class BdgChargeEmp(models.Model):
    employee_id = models.IntegerField()
    charge_id = models.IntegerField()
    total_sum = models.FloatField()
    sum_paid = models.FloatField(blank=True, null=True)
    date = models.BigIntegerField(blank=True, null=True)
    protocol_id = models.IntegerField()
    end_date = models.BigIntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    voice_id = models.IntegerField(blank=True, null=True)
    taxed = models.BooleanField()
    hours = models.IntegerField()
    charges_kind_id = models.IntegerField()
    country = models.IntegerField(blank=True, null=True)
    group_kind = models.TextField(blank=True, null=True)
    diaria_code_kind_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_charge_emp'


class BdgEngageHeader(models.Model):
    number = models.IntegerField()
    eng_date = models.BigIntegerField()
    description = models.TextField()
    con_num = models.CharField(max_length=20, blank=True, null=True)
    con_date = models.BigIntegerField(blank=True, null=True)
    auto = models.BooleanField(blank=True, null=True)
    budget_id = models.IntegerField(blank=True, null=True)
    paid = models.SmallIntegerField(blank=True, null=True)
    cdr_type = models.SmallIntegerField(blank=True, null=True)
    creditor = models.IntegerField(blank=True, null=True)
    tot_sum = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_engage_header'


class BdgEngageItems(models.Model):
    header_id = models.IntegerField()
    project_id = models.IntegerField()
    voice_id = models.IntegerField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)
    sum = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_engage_items'


class BdgEngageLog(models.Model):
    engage_header_id = models.IntegerField()
    project_id = models.IntegerField()
    voice_out_id = models.IntegerField(blank=True, null=True)
    sum = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_engage_log'


class BdgInitial(models.Model):
    budget_id = models.IntegerField(blank=True, null=True)
    cassa = models.FloatField(blank=True, null=True)
    cassa_cui = models.FloatField(blank=True, null=True)
    riscosse_a = models.FloatField(blank=True, null=True)
    riscosse_a_cui = models.FloatField(blank=True, null=True)
    riscosse_b = models.FloatField(blank=True, null=True)
    riscosse_b_cui = models.FloatField(blank=True, null=True)
    eseguiti_a = models.FloatField(blank=True, null=True)
    eseguiti_a_cui = models.FloatField(blank=True, null=True)
    eseguiti_b = models.FloatField(blank=True, null=True)
    eseguiti_b_cui = models.FloatField(blank=True, null=True)
    ccp = models.FloatField(blank=True, null=True)
    riscosse_a_ra = models.FloatField(blank=True, null=True)
    eseguiti_a_rp = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_initial'


class BdgModal(models.Model):
    mod_id = models.AutoField(primary_key=True)
    descr = models.CharField(unique=True, max_length=50)

    class Meta:
        managed = False
        db_table = 'bdg_modal'


class BdgOrders(models.Model):
    order_id = models.AutoField(primary_key=True)
    number = models.IntegerField()
    date = models.BigIntegerField(blank=True, null=True)
    status = models.CharField(max_length=15)
    eng_id = models.IntegerField()
    creditor = models.IntegerField()
    creditor_type = models.SmallIntegerField()
    descr = models.TextField()
    to_pay = models.FloatField()
    paid = models.FloatField()
    mod = models.SmallIntegerField(blank=True, null=True)
    del_num = models.CharField(max_length=20, blank=True, null=True)
    del_date = models.BigIntegerField(blank=True, null=True)
    pay_date = models.BigIntegerField(blank=True, null=True)
    budget_id = models.IntegerField(blank=True, null=True)
    bank_abi = models.IntegerField(blank=True, null=True)
    bank_id = models.IntegerField(blank=True, null=True)
    tipologia = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_orders'


class BdgRegistration(models.Model):
    number = models.IntegerField(blank=True, null=True)
    activ_id = models.IntegerField(blank=True, null=True)
    voice_id = models.IntegerField(blank=True, null=True)
    descr = models.CharField(max_length=255, blank=True, null=True)
    summa = models.FloatField(blank=True, null=True)
    creditor = models.CharField(max_length=200, blank=True, null=True)
    payment = models.CharField(max_length=200, blank=True, null=True)
    data = models.BigIntegerField(blank=True, null=True)
    budget_id = models.IntegerField(blank=True, null=True)
    reint = models.SmallIntegerField(blank=True, null=True)
    fund_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_registration'


class BdgResFund(models.Model):
    res_fund_id = models.AutoField(primary_key=True)
    budget_id = models.IntegerField()
    perc_max = models.FloatField()
    sum = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_res_fund'


class BdgRever(models.Model):
    rever_id = models.AutoField(primary_key=True)
    number = models.IntegerField()
    date = models.BigIntegerField()
    asses_id = models.IntegerField()
    descr = models.TextField()
    mod = models.SmallIntegerField(blank=True, null=True)
    del_num = models.CharField(max_length=10, blank=True, null=True)
    del_date = models.BigIntegerField(blank=True, null=True)
    financed = models.CharField(max_length=10)
    status = models.CharField(max_length=15)
    budget_id = models.IntegerField()
    to_pay_in = models.FloatField()
    paid = models.FloatField()
    bank_abi = models.IntegerField(blank=True, null=True)
    tipologia = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_rever'


class BdgRevpay(models.Model):
    revpay_id = models.AutoField(primary_key=True)
    number = models.IntegerField()
    del_num = models.IntegerField(blank=True, null=True)
    protocol_id = models.BigIntegerField()
    budget_id = models.SmallIntegerField()
    date = models.BigIntegerField(blank=True, null=True)
    total_pay = models.FloatField(blank=True, null=True)
    total_rev = models.FloatField(blank=True, null=True)
    bank_abi = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_revpay'


class BdgRpRel(models.Model):
    rp_rel_id = models.AutoField(primary_key=True)
    revpay_id = models.IntegerField()
    rp_id = models.IntegerField()
    rp_type = models.SmallIntegerField()
    rp_sum = models.FloatField()
    rp_num = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'bdg_rp_rel'


class BdgVar(models.Model):
    var_id = models.AutoField(primary_key=True)
    number = models.IntegerField()
    descr = models.TextField()
    prop_num = models.CharField(max_length=10, blank=True, null=True)
    prop_date = models.BigIntegerField(blank=True, null=True)
    appr_num = models.CharField(max_length=10, blank=True, null=True)
    appr_date = models.BigIntegerField(blank=True, null=True)
    total_in = models.FloatField(blank=True, null=True)
    total_out = models.FloatField(blank=True, null=True)
    budget_id = models.IntegerField()
    executed = models.BooleanField(blank=True, null=True)
    transfer = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'bdg_var'


class BdgVarVoice(models.Model):
    vv_id = models.AutoField(primary_key=True)
    budget_id = models.IntegerField()
    voice_id = models.IntegerField()
    vv_sum = models.FloatField()
    var_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_var_voice'


class BdgVoiceSup(models.Model):
    voice_sup_id = models.AutoField(primary_key=True)
    descr = models.CharField(max_length=255, blank=True, null=True)
    voice_id = models.IntegerField()
    supplier_id = models.IntegerField(blank=True, null=True)
    code = models.CharField(max_length=4, blank=True, null=True)
    type = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'bdg_voice_sup'


class BdgVoiceSupType(models.Model):
    vs_type_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'bdg_voice_sup_type'


class BdgVoiceVarA(models.Model):
    voice_var_id = models.AutoField(primary_key=True)
    budget_id = models.SmallIntegerField()
    voice_in_id = models.IntegerField()
    var_id = models.IntegerField()
    var_sum = models.FloatField()
    voice_out_id = models.IntegerField()
    act_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'bdg_voice_var_a'


class BdgVoices(models.Model):
    voice_id = models.AutoField(primary_key=True)
    aggreg_nr = models.SmallIntegerField()
    voice_nr = models.SmallIntegerField(blank=True, null=True)
    description = models.CharField(max_length=200)
    ext_desc = models.TextField(blank=True, null=True)
    subvoice_nr = models.SmallIntegerField(blank=True, null=True)
    inout = models.SmallIntegerField(blank=True, null=True)
    blocked = models.BooleanField(blank=True, null=True)
    statal = models.BooleanField(blank=True, null=True)
    year = models.IntegerField()
    sum = models.FloatField()
    residui = models.FloatField()

    class Meta:
        managed = False
        db_table = 'bdg_voices'


class Cab(models.Model):
    abi = models.CharField(max_length=5, blank=True, null=True)
    cab = models.CharField(max_length=5, blank=True, null=True)
    descr = models.TextField(blank=True, null=True)
    addr = models.TextField(blank=True, null=True)
    abi_id = models.IntegerField(blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'cab'


class CalenderHolidays(models.Model):
    month = models.IntegerField(blank=True, null=True)
    day = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'calender_holidays'


class CalenderWeekends(models.Model):
    week_day = models.IntegerField(primary_key=True)
    weekends = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'calender_weekends'



class CedolinoData(models.Model):
    cedolino_id = models.BigAutoField(primary_key=True)
    rtr_id = models.BigIntegerField(blank=True, null=True)
    cedolino_data = models.TextField()

    class Meta:
        managed = False
        db_table = 'cedolino_data'


class ChargesData(models.Model):
    charges_data_id = models.AutoField(primary_key=True)
    employee_id = models.IntegerField(blank=True, null=True)
    calculation_date = models.BigIntegerField(blank=True, null=True)
    rtr_ch_id = models.BigIntegerField(blank=True, null=True)
    charges_data = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'charges_data'


class ChargesKind(models.Model):
    charges_kind_id = models.AutoField(primary_key=True)
    description = models.TextField()
    pay_hour = models.FloatField()
    date = models.BigIntegerField()
    view_hours = models.BooleanField()
    iva = models.FloatField(blank=True, null=True)
    inps_dip = models.FloatField(blank=True, null=True)
    irpef = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'charges_kind'


class Cities(models.Model):
    city_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=50)
    city_code = models.CharField(max_length=10, blank=True, null=True)
    province = models.CharField(max_length=5, blank=True, null=True)
    region = models.ForeignKey('Regions', models.DO_NOTHING, db_column='region', blank=True, null=True)
    is_city = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'cities'


class CityPerc(models.Model):
    city_code = models.CharField(max_length=7, blank=True, null=True)
    perc = models.FloatField(blank=True, null=True)
    date = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'city_perc'


class CodiciCasse(models.Model):
    casse_id = models.AutoField(primary_key=True)
    codice = models.IntegerField()
    titolo = models.TextField()
    descrizione = models.TextField()

    class Meta:
        managed = False
        db_table = 'codici_casse'


class CodiciCessazione(models.Model):
    cessazione_id = models.AutoField(primary_key=True)
    codice = models.IntegerField()
    titolo = models.TextField()
    descrizione = models.TextField()

    class Meta:
        managed = False
        db_table = 'codici_cessazione'


class CodiciTipoImpiego(models.Model):
    impiego_id = models.AutoField(primary_key=True)
    codice = models.IntegerField()
    titolo = models.TextField()
    descrizione = models.TextField()

    class Meta:
        managed = False
        db_table = 'codici_tipo_impiego'


class CodiciTipoServizio(models.Model):
    servizio_id = models.AutoField(primary_key=True)
    codice = models.IntegerField()
    titolo = models.TextField()
    descrizione = models.TextField()

    class Meta:
        managed = False
        db_table = 'codici_tipo_servizio'


class Contact(models.Model):
    contact_id = models.AutoField(primary_key=True)
    address = models.TextField(blank=True, null=True)
    phone_num = models.TextField(blank=True, null=True)
    fax = models.TextField(blank=True, null=True)
    city = models.ForeignKey(Cities, models.DO_NOTHING, blank=True, null=True)
    email = models.TextField(blank=True, null=True)
    mobile = models.TextField(blank=True, null=True)
    web = models.TextField(blank=True, null=True)
    cap = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'contact'


class ContestType(models.Model):
    cont_code = models.CharField(max_length=4)
    u_code = models.CharField(max_length=4, blank=True, null=True)
    description = models.CharField(max_length=100, blank=True, null=True)
    aa = models.CharField(max_length=4, blank=True, null=True)
    ee = models.CharField(max_length=4, blank=True, null=True)
    miv = models.CharField(max_length=4, blank=True, null=True)
    ss = models.CharField(max_length=4, blank=True, null=True)
    os = models.CharField(max_length=2, blank=True, null=True)
    rom_code = models.CharField(max_length=10, blank=True, null=True)
    u_code_g = models.CharField(max_length=4, blank=True, null=True)
    fascia = models.CharField(max_length=50, blank=True, null=True)
    det_description = models.CharField(max_length=100, blank=True, null=True)
    contest_type_id = models.AutoField(primary_key=True)

    class Meta:
        managed = False
        db_table = 'contest_type'


class Contracts(models.Model):
    contract_id = models.AutoField(primary_key=True)
    protocol = models.BigIntegerField()
    revocation = models.BigIntegerField(blank=True, null=True)
    employee_id = models.IntegerField()
    activity_type = models.CharField(max_length=5, blank=True, null=True)
    flag_canc = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'contracts'




class CoreContactType(models.Model):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20)

    class Meta:
        managed = False
        db_table = 'core_contact_type'


class CoreContactgroups(models.Model):
    name = models.CharField(max_length=255)
    user = models.ForeignKey('Users', models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'core_contactgroups'


class CoreContacts(models.Model):
    name = models.CharField(max_length=255)
    email = models.CharField(unique=True, max_length=255)
    contact_type = models.ForeignKey(CoreContactType, models.DO_NOTHING)
    user = models.ForeignKey('Users', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'core_contacts'


class CoreContactsContactgroups(models.Model):
    contact = models.ForeignKey(CoreContacts, models.DO_NOTHING)
    group = models.ForeignKey(CoreContactgroups, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'core_contacts_contactgroups'
        unique_together = (('contact', 'group'),)


class CoreFileImports(models.Model):
    import_date = models.IntegerField(blank=True, null=True)
    imported_file_name = models.CharField(max_length=255, blank=True, null=True)
    footprint = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'core_file_imports'


class CoreForcedAction(models.Model):
    date = models.DateTimeField(blank=True, null=True)
    section = models.CharField(max_length=16, blank=True, null=True)
    code = models.CharField(max_length=32, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    table = models.CharField(max_length=32, blank=True, null=True)
    id_value = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'core_forced_action'


class CorePrintSpool(models.Model):
    user_id = models.IntegerField()
    name = models.CharField(unique=True, max_length=100)
    path = models.CharField(max_length=100)
    completed = models.BooleanField()
    params = models.TextField()
    notified = models.BooleanField()
    mime = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'core_print_spool'


class CoreTableRowImports(models.Model):
    row_model_name = models.CharField(max_length=50, blank=True, null=True)
    row_table_id = models.IntegerField(blank=True, null=True)
    import_id = models.IntegerField(blank=True, null=True)
    row_data_footprint = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'core_table_row_imports'


class Countries(models.Model):
    country_id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=5, blank=True, null=True)
    description = models.CharField(max_length=50, blank=True, null=True)
    default = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'countries'


class CudData(models.Model):
    cud_id = models.AutoField(primary_key=True)
    employee_id = models.BigIntegerField()
    year = models.IntegerField()
    cud_data = models.TextField()
    rtr_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'cud_data'


class Decreti(models.Model):
    absence_kind = models.CharField(max_length=10)
    employee_kind = models.CharField(max_length=10)
    employee_role = models.CharField(max_length=10)
    html = models.TextField()
    time_back = models.CharField(max_length=50)
    prev_abs_kind = models.CharField(max_length=150, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'decreti'


class DiariaCodeKind(models.Model):
    diaria_id = models.AutoField(primary_key=True)
    descr_diaria = models.TextField()
    perc_diaria = models.FloatField()
    descr_quota = models.TextField()
    money_quota = models.FloatField()

    class Meta:
        managed = False
        db_table = 'diaria_code_kind'


class DirecType(models.Model):
    description = models.CharField(max_length=100)
    direc_code = models.CharField(primary_key=True, max_length=2)

    class Meta:
        managed = False
        db_table = 'direc_type'


class DmaData(models.Model):
    dma_id = models.AutoField(primary_key=True)
    rtr_id = models.BigIntegerField(blank=True, null=True)
    dma_data = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'dma_data'


class Dossier(models.Model):
    name = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'dossier'


class Employee(models.Model):
    employee_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    surname = models.CharField(max_length=50)
    gender = models.CharField(max_length=10, blank=True, null=True)
    birthdate = models.BigIntegerField(blank=True, null=True)
    fiscal_code = models.CharField(max_length=30, blank=True, null=True)
    residence = models.ForeignKey(Contact, models.DO_NOTHING, blank=True, null=True)
    address = models.ForeignKey(Contact, models.DO_NOTHING, blank=True, null=True)
    part_spesa = models.CharField(max_length=16, blank=True, null=True)
    bank = models.IntegerField(blank=True, null=True)
    liq_office = models.CharField(max_length=4, blank=True, null=True)
    inps = models.CharField(max_length=17, blank=True, null=True)
    insur_qual = models.CharField(max_length=1, blank=True, null=True)
    fore = models.BooleanField(blank=True, null=True)
    asl = models.CharField(max_length=10, blank=True, null=True)
    adm_code = models.CharField(max_length=16, blank=True, null=True)
    way_pay = models.CharField(max_length=2, blank=True, null=True)
    liquid_group = models.CharField(max_length=4, blank=True, null=True)
    contr_code = models.CharField(max_length=2, blank=True, null=True)
    contr_type = models.CharField(max_length=5, blank=True, null=True)
    contr_cat = models.SmallIntegerField(blank=True, null=True)
    ssp_frm_pmnt = models.SmallIntegerField(blank=True, null=True)
    personal_data = models.SmallIntegerField(blank=True, null=True)
    susp = models.BooleanField(blank=True, null=True)
    payment_group = models.IntegerField(blank=True, null=True)
    priv_ret_type = models.CharField(max_length=5, blank=True, null=True)
    social_position = models.SmallIntegerField(blank=True, null=True)
    active = models.BooleanField(blank=True, null=True)
    statal_code = models.CharField(max_length=16, blank=True, null=True)
    fiscal_city_code = models.CharField(max_length=16, blank=True, null=True)
    birthplace = models.TextField(blank=True, null=True)
    income = models.BigIntegerField(blank=True, null=True)
    state_birth = models.CharField(max_length=5, blank=True, null=True)
    citizenship = models.CharField(max_length=5, blank=True, null=True)
    id_sissi = models.CharField(max_length=5, blank=True, null=True)
    dom_first_prev_year = models.IntegerField(blank=True, null=True)
    dom_last_prev_year = models.IntegerField(blank=True, null=True)
    dom_first_curr_year = models.IntegerField(blank=True, null=True)
    qualification = models.TextField(blank=True, null=True)
    liquid_office_id = models.IntegerField()
    badge_number = models.BigIntegerField(blank=True, null=True)
    tolerance_in = models.IntegerField()
    tolerance_out = models.IntegerField()
    flexibility = models.IntegerField()
    generic_tolerance = models.IntegerField()
    negative_round = models.IntegerField()
    recover_hours = models.IntegerField()
    max_extraordinary_in = models.IntegerField()
    max_extraordinary_out = models.IntegerField()
    min_extraordinary_in = models.IntegerField()
    min_extraordinary_out = models.IntegerField()
    step_out = models.IntegerField()
    step_in = models.IntegerField()
    max_break = models.IntegerField()
    max_cont_work = models.IntegerField()
    simplified_ata_settings = models.BooleanField()
    tolerance_in_und = models.IntegerField()
    tolerance_out_und = models.IntegerField()
    max_undefined_in = models.IntegerField()
    max_undefined_out = models.IntegerField()
    min_undefined_in = models.IntegerField()
    min_undefined_out = models.IntegerField()
    step_out_und = models.IntegerField()
    step_in_und = models.IntegerField()
    undefined_parameter_active = models.BooleanField()
    min_extraordinary_total = models.IntegerField()
    max_extraordinary_total = models.IntegerField()
    min_undefined_total = models.IntegerField()
    max_undefined_total = models.IntegerField()
    step_total_undefined = models.IntegerField()
    step_total_extraordinary = models.IntegerField()
    lunch_duration = models.IntegerField()
    lunch_deductible = models.BooleanField()
    service_deductible = models.BooleanField()
    min_undefined_lunch = models.IntegerField()
    min_extraordinary_lunch = models.IntegerField()
    max_undefined_lunch = models.IntegerField()
    max_extraordinary_lunch = models.IntegerField()
    step_lunch_undefined = models.IntegerField()
    step_lunch_extraordinary = models.IntegerField()
    break_after_max_work = models.IntegerField()
    unit_recover_hours = models.CharField(max_length=3)
    max_work = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'employee'


class EmployeeFiscalDetails(models.Model):
    id = models.BigAutoField(primary_key=True)
    uid = models.BigIntegerField()
    primo_acconto = models.TextField(blank=True, null=True)
    secondo_o_unico = models.TextField(blank=True, null=True)
    acconti_per_ipref = models.TextField(blank=True, null=True)
    acconto_addizzionale_communale = models.TextField(blank=True, null=True)
    acconto_addizzionale_communale_sospeso = models.TextField(blank=True, null=True)
    credito_non_rimborsato = models.TextField(blank=True, null=True)
    credito_reggionale = models.TextField(blank=True, null=True)
    credito_communale = models.TextField(blank=True, null=True)
    casi_particolari = models.TextField(blank=True, null=True)
    applicazione_maggiore = models.TextField(blank=True, null=True)
    contributi_previdenza = models.TextField(blank=True, null=True)
    contributi_versati = models.TextField(blank=True, null=True)
    previdenza_complementare = models.TextField(blank=True, null=True)
    eventi_eccezionali = models.TextField(blank=True, null=True)
    perc_irpef = models.IntegerField()
    perc_max = models.IntegerField()
    cass_prev_proff = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'employee_fiscal_details'


class EmploymentMotiv(models.Model):
    code = models.CharField(max_length=5)
    description = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'employment_motiv'


class EventError(models.Model):
    level_nr = models.IntegerField()
    level_descr = models.TextField()
    description = models.TextField()
    path = models.TextField()
    line = models.TextField()
    error_time = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'event_error'


class EventLog(models.Model):
    event_id = models.BigAutoField(primary_key=True)
    func_name = models.TextField()
    args = models.TextField(blank=True, null=True)
    uid = models.BigIntegerField()
    event_time = models.BigIntegerField()
    remote_addr = models.TextField(blank=True, null=True)
    http_user_agent = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'event_log'


class Extensions(models.Model):
    extension_id = models.AutoField(primary_key=True)
    contract_protocol = models.BigIntegerField()
    extend_date = models.BigIntegerField()
    extend_start = models.BigIntegerField(blank=True, null=True)
    extend_end = models.BigIntegerField(blank=True, null=True)
    protocol_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'extensions'


class ExternalExport(models.Model):
    external_software = models.ForeignKey('ExternalSoftware', models.DO_NOTHING)
    creation_date = models.DateTimeField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'external_export'


class ExternalExportRow(models.Model):
    external_export = models.ForeignKey(ExternalExport, models.DO_NOTHING)
    object_type = models.CharField(max_length=255)
    object_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'external_export_row'


class ExternalMissions(models.Model):
    mission_id = models.AutoField(primary_key=True)
    country = models.TextField()
    group_1 = models.FloatField()
    group_2 = models.FloatField()
    group_3 = models.FloatField()
    group_4 = models.FloatField()
    group_5_9 = models.FloatField()
    group_10_11 = models.FloatField()

    class Meta:
        managed = False
        db_table = 'external_missions'


class ExternalSoftware(models.Model):
    name = models.TextField()

    class Meta:
        managed = False
        db_table = 'external_software'


class ExtraordinaryStored(models.Model):
    extraordinary_stored_id = models.AutoField(primary_key=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    date = models.BigIntegerField()
    recover_hours = models.IntegerField()
    extraordinary = models.IntegerField()
    max_extraordinary = models.IntegerField()
    to_recover = models.IntegerField()
    to_pay = models.IntegerField()
    authorized = models.IntegerField()
    authorized_undefined = models.IntegerField()
    undefined = models.IntegerField()
    note = models.TextField()

    class Meta:
        managed = False
        db_table = 'extraordinary_stored'


class FamilySituation(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(unique=True, max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'family_situation'


class FinanceSit(models.Model):
    finance_sit_id = models.AutoField(primary_key=True)
    payment_group = models.IntegerField(blank=True, null=True)
    extern_adm = models.BooleanField(blank=True, null=True)
    zip_code = models.IntegerField(blank=True, null=True)
    payment_way = models.CharField(max_length=5, blank=True, null=True)
    inps_pos = models.CharField(max_length=5, blank=True, null=True)
    fam_sit = models.CharField(max_length=5, blank=True, null=True)
    priv_ret_type = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'finance_sit'


class FiscalData(models.Model):
    fiscal_data_id = models.BigAutoField(primary_key=True)
    employee_id = models.IntegerField(blank=True, null=True)
    calculation_date = models.BigIntegerField(blank=True, null=True)
    fiscal_data = models.TextField(blank=True, null=True)
    rtr_id = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'fiscal_data'


class FiscalDocuments(models.Model):
    fiscal_document_id = models.AutoField(primary_key=True)
    doc_type = models.IntegerField()
    document = models.CharField(max_length=255)
    filter = models.CharField(max_length=255, blank=True, null=True)
    creation_date = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'fiscal_documents'


class FmlMembers(models.Model):
    employee_id = models.IntegerField()
    name = models.CharField(max_length=50)
    surname = models.CharField(max_length=50)
    brth_place = models.BigIntegerField(blank=True, null=True)
    brth_state = models.CharField(max_length=5, blank=True, null=True)
    brth_date = models.BigIntegerField(blank=True, null=True)
    relation = models.SmallIntegerField()
    profession = models.CharField(max_length=100, blank=True, null=True)
    handicap = models.BooleanField(blank=True, null=True)
    irpef = models.BooleanField(blank=True, null=True)
    under_3 = models.BooleanField(blank=True, null=True)
    req_done = models.BooleanField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    sex = models.CharField(max_length=6, blank=True, null=True)
    id_sissi = models.CharField(max_length=16, blank=True, null=True)
    fgl_50 = models.BooleanField(blank=True, null=True)
    ass_fml = models.FloatField()

    class Meta:
        managed = False
        db_table = 'fml_members'


class FmlRelation(models.Model):
    id = models.IntegerField(primary_key=True)
    description = models.CharField(unique=True, max_length=50)

    class Meta:
        managed = False
        db_table = 'fml_relation'


class FromPayment(models.Model):
    id = models.SmallIntegerField(primary_key=True)
    descr = models.CharField(max_length=25, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'from_payment'


class GatePresences(models.Model):
    badge = models.BigIntegerField()
    date = models.BigIntegerField()
    function_call = models.IntegerField()
    project_id = models.BigIntegerField()
    employee_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'gate_presences'


class Gross(models.Model):
    stage = models.SmallIntegerField(blank=True, null=True)
    prof_id = models.SmallIntegerField(blank=True, null=True)
    salary = models.CharField(max_length=8, blank=True, null=True)
    date = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'gross'


class Groups(models.Model):
    gid = models.AutoField(primary_key=True)
    group_name = models.CharField(max_length=255, blank=True, null=True)
    enabled = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'groups'


class Grpermissions(models.Model):
    permission_id = models.BigAutoField(primary_key=True)
    zone_id = models.IntegerField(blank=True, null=True)
    gid = models.BigIntegerField(blank=True, null=True)
    mode_id = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'grpermissions'


class HelpTopics(models.Model):
    topic_id = models.BigAutoField(primary_key=True)
    location = models.TextField()
    tooltip = models.TextField()
    content = models.TextField()
    topic_name = models.TextField()

    class Meta:
        managed = False
        db_table = 'help_topics'


class Iis(models.Model):
    stage = models.SmallIntegerField(blank=True, null=True)
    prof_id = models.SmallIntegerField(blank=True, null=True)
    salary = models.CharField(max_length=7, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'iis'


class Inps(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'inps'


class Institute(models.Model):
    institute_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    mechan_code = models.CharField(max_length=16)
    contact = models.OneToOneField(Contact, models.DO_NOTHING, blank=True, null=True)
    fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    school_type = models.CharField(max_length=3, blank=True, null=True)
    parent = models.IntegerField(blank=True, null=True)
    def_field = models.BooleanField(db_column='def', blank=True, null=True)  # Field renamed because it was a Python reserved word.
    dir_name = models.CharField(max_length=100, blank=True, null=True)
    dir_surname = models.CharField(max_length=100, blank=True, null=True)
    adir_name = models.CharField(max_length=100, blank=True, null=True)
    adir_surname = models.CharField(max_length=100, blank=True, null=True)
    pres_ge_name = models.CharField(max_length=100, blank=True, null=True)
    pres_ge_surname = models.CharField(max_length=100, blank=True, null=True)
    seg_cons_name = models.CharField(max_length=100, blank=True, null=True)
    seg_cons_surname = models.CharField(max_length=100, blank=True, null=True)
    pres_con_name = models.CharField(max_length=100, blank=True, null=True)
    pres_con_surname = models.CharField(max_length=100, blank=True, null=True)
    dir_fiscal_code = models.TextField(blank=True, null=True)
    school_fiscal_code = models.TextField(blank=True, null=True)
    inpdap_code = models.TextField(blank=True, null=True)
    assicurazioni_sanitarie = models.TextField(blank=True, null=True)
    dir_sesso = models.CharField(max_length=1)
    dir_birth = models.BigIntegerField()
    dir_city = models.TextField()
    postal_account = models.BigIntegerField(blank=True, null=True)
    ateco_code = models.TextField(blank=True, null=True)
    activity_code = models.TextField(blank=True, null=True)
    dir_curr_addr = models.TextField()
    dir_curr_city = models.TextField()
    dir_curr_phone = models.TextField()
    dir_emp = models.ForeignKey(Employee, models.DO_NOTHING, blank=True, null=True)
    adir_emp_id = models.IntegerField()
    presge_emp_id = models.IntegerField()
    segcons_emp_id = models.IntegerField()
    prescon_emp_id = models.IntegerField()
    respacq_emp_id = models.IntegerField()
    job_director_id = models.IntegerField(blank=True, null=True)
    job_vice_director_id = models.IntegerField(blank=True, null=True)
    job_dsga_id = models.IntegerField(blank=True, null=True)
    job_personnel_id = models.IntegerField(blank=True, null=True)
    job_accounting_id = models.IntegerField(blank=True, null=True)
    job_warehouse_id = models.IntegerField(blank=True, null=True)
    job_registry_id = models.IntegerField(blank=True, null=True)
    ipa_code = models.CharField(max_length=255, blank=True, null=True)
    ae_fiscal_code = models.CharField(max_length=16, blank=True, null=True)
    ade_email = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'institute'


class InvInitial(models.Model):
    inv_initial_id = models.AutoField(primary_key=True)
    budget_year = models.IntegerField()
    initial_cat1 = models.FloatField()
    initial_cat2 = models.FloatField()
    initial_cat3 = models.FloatField()
    initial_cat4 = models.FloatField()
    initial_nr_cat1 = models.BigIntegerField()
    initial_nr_cat2 = models.BigIntegerField()
    initial_nr_cat3 = models.BigIntegerField()
    initial_nr_cat4 = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'inv_initial'


class Invoice(models.Model):
    fiscal_code = models.ForeignKey('Supplier', models.DO_NOTHING, db_column='fiscal_code', blank=True, null=True)
    vat_number = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255)
    address = models.CharField(max_length=255)
    city_id = models.IntegerField(blank=True, null=True)
    city_name = models.CharField(max_length=255)
    city_province = models.CharField(max_length=255)
    city_postal_code = models.IntegerField()
    total = models.FloatField()
    xml = models.TextField(blank=True, null=True)
    number = models.CharField(max_length=255)
    date = models.DateTimeField()
    archive_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'invoice'


class InvoiceExpiration(models.Model):
    invoice = models.ForeignKey(Invoice, models.DO_NOTHING)
    expiration = models.DateTimeField()

    class Meta:
        managed = False
        db_table = 'invoice_expiration'


class Langs(models.Model):
    etalon = models.TextField(blank=True, null=True)
    ita = models.TextField(blank=True, null=True)
    eng = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'langs'


class LiqGroup(models.Model):
    code = models.CharField(max_length=4, blank=True, null=True)
    descr = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'liq_group'


class LiquidOffice(models.Model):
    liquid_office_id = models.AutoField(primary_key=True)
    description = models.TextField()

    class Meta:
        managed = False
        db_table = 'liquid_office'


class Locks(models.Model):
    lock_id = models.BigAutoField(primary_key=True)
    uid = models.BigIntegerField()
    locktime = models.BigIntegerField()
    operation = models.TextField()
    record_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'locks'


class LoggedWidgets(models.Model):
    id = models.BigAutoField(primary_key=True)
    widget_id = models.BigIntegerField(unique=True, blank=True, null=True)
    widget_name = models.CharField(max_length=255, blank=True, null=True)
    template_name = models.CharField(max_length=255, blank=True, null=True)
    widget_type = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'logged_widgets'


class LoggedWidgetsGroup(models.Model):
    id = models.BigAutoField(primary_key=True)
    widget_id = models.BigIntegerField(blank=True, null=True)
    gid = models.BigIntegerField(blank=True, null=True)
    mode_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'logged_widgets_group'


class LoggedWidgetsUser(models.Model):
    id = models.BigAutoField(primary_key=True)
    widget_id = models.BigIntegerField(blank=True, null=True)
    uid = models.BigIntegerField(blank=True, null=True)
    mode = models.ForeignKey('Modes', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'logged_widgets_user'


class LongTerm(models.Model):
    long_term_id = models.AutoField(primary_key=True)
    role_kind = models.CharField(max_length=2, blank=True, null=True)
    lt_reason = models.CharField(max_length=2, blank=True, null=True)
    expiration_date = models.BigIntegerField(blank=True, null=True)
    school_rec_app = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'long_term'


class MagisterSync(models.Model):
    id_esterno = models.CharField(max_length=255)
    modalita_causale = models.CharField(max_length=255)
    causale = models.IntegerField(blank=True, null=True)
    causale_codice = models.CharField(max_length=255)
    importo = models.FloatField()
    data_scadenza = models.DateTimeField(blank=True, null=True)
    data_operazione = models.DateTimeField(blank=True, null=True)
    descrizione = models.TextField(blank=True, null=True)
    sent_date = models.DateTimeField(blank=True, null=True)
    creation_date = models.DateTimeField()
    error = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'magister_sync'


class MastercomTeacherLink(models.Model):
    mc_id = models.BigIntegerField()
    mc2_id = models.BigIntegerField(primary_key=True)

    class Meta:
        managed = False
        db_table = 'mastercom_teacher_link'


class MeasureUnits(models.Model):
    description = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'measure_units'


class Mod770S(models.Model):
    mod770_id = models.AutoField(primary_key=True)
    year = models.BigIntegerField(blank=True, null=True)
    calculation_date = models.BigIntegerField(blank=True, null=True)
    mod770_data = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'mod770_s'


class Modes(models.Model):
    mode_id = models.AutoField(primary_key=True)
    mode_description = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'modes'


class Parameter(models.Model):
    parameter_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50)
    value = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'parameter'


class PaymentGroup(models.Model):
    payment_group_id = models.IntegerField(primary_key=True)
    year = models.IntegerField(blank=True, null=True)
    description = models.CharField(max_length=50, blank=True, null=True)
    fiscal_treat = models.CharField(max_length=20, blank=True, null=True)
    com_tkt_meth = models.CharField(max_length=50, blank=True, null=True)
    add_tkt_meth = models.CharField(max_length=50, blank=True, null=True)
    com_tkt_div = models.IntegerField(blank=True, null=True)
    add_tkt_div = models.IntegerField(blank=True, null=True)
    tx_clc_month_py = models.IntegerField(blank=True, null=True)
    day_mult_tx_clc_py = models.IntegerField(blank=True, null=True)
    calc_method = models.CharField(max_length=100, blank=True, null=True)
    add_deduct = models.BooleanField(blank=True, null=True)
    code = models.CharField(max_length=3)

    class Meta:
        managed = False
        db_table = 'payment_group'


class PaymentWay(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(unique=True, max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'payment_way'


class PaymentsClass(models.Model):
    class_field = models.CharField(db_column='class', primary_key=True, max_length=3)  # Field renamed because it was a Python reserved word.
    description = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'payments_class'


class PermissionVacationTimeline(models.Model):
    permission_vacation_timeline_id = models.AutoField(primary_key=True)
    period_start = models.BigIntegerField()
    period_end = models.BigIntegerField()
    permission_hours = models.IntegerField()
    vacation_hours = models.IntegerField()
    service_state_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'permission_vacation_timeline'


class Permissions(models.Model):
    permission_id = models.BigIntegerField()
    zone = models.ForeignKey('Zone', models.DO_NOTHING, blank=True, null=True)
    mode = models.ForeignKey(Modes, models.DO_NOTHING, blank=True, null=True)
    gid = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'permissions'


class PersonnelPresences(models.Model):
    personnel_presence_id = models.AutoField(primary_key=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    project_id = models.BigIntegerField(blank=True, null=True)
    project_edit = models.ForeignKey(BdgActivities, models.DO_NOTHING, blank=True, null=True)
    date = models.BigIntegerField(blank=True, null=True)
    date_edit = models.BigIntegerField(blank=True, null=True)
    type = models.IntegerField()
    type_edit = models.IntegerField()
    original_inout = models.IntegerField()
    original_inout_edit = models.IntegerField()
    description = models.CharField(max_length=2000)
    hour_type_id = models.BigIntegerField()
    hour_type_edit_id = models.BigIntegerField()
    insertion_mode = models.CharField(max_length=1)

    class Meta:
        managed = False
        db_table = 'personnel_presences'


class PersonnelProjects(models.Model):
    personnel_projects_id = models.AutoField(primary_key=True)
    project = models.ForeignKey(BdgActivities, models.DO_NOTHING, blank=True, null=True)
    personnel = models.ForeignKey(Employee, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'personnel_projects'


class PersonnelStacks(models.Model):
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    stack = models.ForeignKey(AbsenceStack, models.DO_NOTHING)
    reset_quota = models.FloatField()

    class Meta:
        managed = False
        db_table = 'personnel_stacks'


class PersonnelTimetable(models.Model):
    personnel_timetable_id = models.AutoField(primary_key=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    date_start = models.BigIntegerField()
    date_end = models.BigIntegerField()
    date_start_pause = models.BigIntegerField(blank=True, null=True)
    date_end_pause = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'personnel_timetable'


class PosKind(models.Model):
    code = models.CharField(primary_key=True, max_length=3)
    description = models.CharField(max_length=50, blank=True, null=True)
    aa = models.BooleanField(blank=True, null=True)
    ee = models.BooleanField(blank=True, null=True)
    miv = models.BooleanField(blank=True, null=True)
    ss = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'pos_kind'


class PrintJob(models.Model):
    id_print_job = models.AutoField(primary_key=True)
    print_data = models.TextField(blank=True, null=True)
    print_date = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'print_job'


class PrintTemplates(models.Model):
    print_id = models.AutoField(primary_key=True)
    ord = models.BigIntegerField()
    code = models.CharField(max_length=255)
    parameter1 = models.TextField(blank=True, null=True)
    parameter2 = models.TextField(blank=True, null=True)
    date = models.BigIntegerField()
    string = models.TextField()
    align = models.CharField(max_length=1)
    style = models.CharField(max_length=1)
    font_size = models.IntegerField()
    margin_top = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'print_templates'


class PrivRetType(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(unique=True, max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'priv_ret_type'


class ProfProf(models.Model):
    description = models.CharField(max_length=70, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'prof_prof'


class ProfProfile(models.Model):
    prof_profile_code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(max_length=100, blank=True, null=True)
    aa = models.CharField(max_length=5, blank=True, null=True)
    ee = models.CharField(max_length=5, blank=True, null=True)
    miv = models.CharField(max_length=5, blank=True, null=True)
    ss = models.CharField(max_length=5, blank=True, null=True)
    fascia = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'prof_profile'


class ProtocolAction(models.Model):
    description = models.CharField(max_length=100)
    comment = models.CharField(max_length=255, blank=True, null=True)
    active = models.BooleanField()
    type = models.ForeignKey('ProtocolType', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_action'


class ProtocolCorrespondent(models.Model):
    title = models.TextField()
    note = models.TextField(blank=True, null=True)
    legal_person = models.BooleanField(blank=True, null=True)
    correspondent_type_id = models.BigIntegerField(blank=True, null=True)
    correspondent_type = models.CharField(max_length=1)
    fiscal_code = models.CharField(max_length=50, blank=True, null=True)
    city_id = models.IntegerField(blank=True, null=True)
    zipcode = models.CharField(max_length=5, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=64, blank=True, null=True)
    fax = models.CharField(max_length=64, blank=True, null=True)
    mobile = models.CharField(max_length=64, blank=True, null=True)
    email = models.CharField(max_length=64, blank=True, null=True)
    web = models.CharField(max_length=128, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_correspondent'


class ProtocolProtocol(models.Model):
    id = models.BigAutoField(primary_key=True)
    date = models.BigIntegerField()
    note = models.TextField(blank=True, null=True)
    budget_id = models.SmallIntegerField(blank=True, null=True)
    type = models.ForeignKey('ProtocolType', models.DO_NOTHING, blank=True, null=True)
    rec_id = models.IntegerField(blank=True, null=True)
    rec_type = models.SmallIntegerField(blank=True, null=True)
    protocol_number = models.IntegerField()
    obj_id = models.IntegerField(blank=True, null=True)
    direction = models.CharField(max_length=1, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    correspondents_text = models.TextField(blank=True, null=True)
    canceled = models.BooleanField()
    external_act_number = models.TextField(blank=True, null=True)
    send_method = models.ForeignKey('ProtocolSendMethod', models.DO_NOTHING, blank=True, null=True)
    dossier = models.TextField(blank=True, null=True)
    subject_kind = models.ForeignKey('ProtocolSubjectKind', models.DO_NOTHING, blank=True, null=True)
    reserved = models.BooleanField()
    header_position = models.CharField(max_length=255, blank=True, null=True)
    mail_sending = models.DateTimeField(blank=True, null=True)
    mail_error = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_protocol'


class ProtocolProtocolCorrespondent(models.Model):
    protocol = models.OneToOneField(ProtocolProtocol, models.DO_NOTHING, primary_key=True)
    correspondent = models.ForeignKey(ProtocolCorrespondent, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'protocol_protocol_correspondent'
        unique_together = (('protocol', 'correspondent'),)


class ProtocolProtocolDocument(models.Model):
    protocol = models.OneToOneField(ProtocolProtocol, models.DO_NOTHING, primary_key=True)
    document = models.ForeignKey(ArchiveDocumentFile, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'protocol_protocol_document'
        unique_together = (('protocol', 'document'),)


class ProtocolProtocolHistory(models.Model):
    action = models.CharField(max_length=1)
    date = models.BigIntegerField()
    protocol = models.ForeignKey(ProtocolProtocol, models.DO_NOTHING)
    user = models.ForeignKey('Users', models.DO_NOTHING)
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_protocol_history'


class ProtocolProtocolProtocol(models.Model):
    protocol_1 = models.OneToOneField(ProtocolProtocol, models.DO_NOTHING, primary_key=True)
    protocol_2 = models.ForeignKey(ProtocolProtocol, models.DO_NOTHING)
    parent_relationship = models.BooleanField()

    class Meta:
        managed = False
        db_table = 'protocol_protocol_protocol'
        unique_together = (('protocol_1', 'protocol_2'),)


class ProtocolRegister(models.Model):
    code = models.IntegerField(unique=True)
    close_date = models.DateTimeField(blank=True, null=True)
    creator_software = models.CharField(max_length=255)
    creator_person = models.CharField(max_length=255)
    recipient = models.CharField(max_length=255, blank=True, null=True)
    hash = models.CharField(max_length=32)
    ipa_code = models.CharField(max_length=255)
    school_name = models.CharField(max_length=255)
    area_code = models.IntegerField()
    responsible = models.CharField(max_length=255)
    subject = models.CharField(max_length=255)
    register_code = models.IntegerField()
    register_number = models.IntegerField(unique=True)
    year = models.IntegerField()
    first_registration_number = models.IntegerField(blank=True, null=True)
    last_registration_number = models.IntegerField(blank=True, null=True)
    first_registration_date = models.DateTimeField(blank=True, null=True)
    last_registration_date = models.DateTimeField(blank=True, null=True)
    file = models.TextField()
    archived = models.DateTimeField(blank=True, null=True)
    archive_document = models.ForeignKey(ArchiveDocument, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_register'


class ProtocolSendMethod(models.Model):
    title = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'protocol_send_method'


class ProtocolSubjectKind(models.Model):
    title = models.CharField(max_length=100)

    class Meta:
        managed = False
        db_table = 'protocol_subject_kind'


class ProtocolType(models.Model):
    code = models.CharField(max_length=20)
    description = models.CharField(max_length=255, blank=True, null=True)
    parent_type = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'protocol_type'
        unique_together = (('code', 'parent_type'),)


class Province(models.Model):
    province_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=50)
    initials = models.CharField(max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'province'


class RegionPerc(models.Model):
    region_code = models.CharField(max_length=2, blank=True, null=True)
    frm = models.FloatField(blank=True, null=True)
    too = models.FloatField(blank=True, null=True)
    perc = models.FloatField(blank=True, null=True)
    date = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'region_perc'


class Regions(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    name = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'regions'


class Report(models.Model):
    id = models.IntegerField(primary_key=True)
    date_created = models.DateTimeField()
    print_key = models.CharField(max_length=255)
    print_type = models.CharField(max_length=255)
    subject_id = models.IntegerField(blank=True, null=True)
    subject_type = models.CharField(max_length=1, blank=True, null=True)
    school_year = models.CharField(max_length=255, blank=True, null=True)
    template_id = models.IntegerField(blank=True, null=True)
    subject_data = models.CharField(max_length=255, blank=True, null=True)
    subject_class = models.CharField(max_length=100, blank=True, null=True)
    subject_school_address_code = models.CharField(max_length=20, blank=True, null=True)
    published = models.BooleanField(blank=True, null=True)
    status = models.CharField(max_length=20, blank=True, null=True)
    print_params = models.TextField(blank=True, null=True)
    last_result = models.TextField(blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    filepath = models.CharField(max_length=255, blank=True, null=True)
    report_key = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'report'


class RetrCateg(models.Model):
    description = models.CharField(max_length=100)
    code = models.CharField(primary_key=True, max_length=5)

    class Meta:
        managed = False
        db_table = 'retr_categ'


class Revocation(models.Model):
    revoc_id = models.AutoField(primary_key=True)
    revoc_date = models.BigIntegerField()
    revoc_reason = models.TextField(blank=True, null=True)
    protocol_id = models.BigIntegerField()
    contract_id = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'revocation'


class RoleReason(models.Model):
    description = models.CharField(max_length=100)
    code = models.CharField(max_length=2, blank=True, null=True)
    ata = models.BooleanField(blank=True, null=True)
    teacher = models.BooleanField(blank=True, null=True)
    leading = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'role_reason'


class RoleType(models.Model):
    code = models.CharField(primary_key=True, max_length=2)
    description = models.CharField(max_length=50, blank=True, null=True)
    ata = models.BooleanField(blank=True, null=True)
    teacher = models.BooleanField(blank=True, null=True)
    lead = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'role_type'


class RtrCharges(models.Model):
    pcheck_ch_id = models.AutoField(primary_key=True)
    pcheck_date = models.BigIntegerField(blank=True, null=True)
    period_start = models.BigIntegerField(blank=True, null=True)
    period_end = models.BigIntegerField()
    tot_gr = models.FloatField()
    inpdap = models.FloatField()
    opfg = models.FloatField()
    tax_inc = models.FloatField()
    st_tax = models.FloatField()
    net = models.FloatField()
    st_inps = models.FloatField()
    st_irap = models.FloatField()
    tot_tax = models.FloatField()
    employee_id = models.IntegerField(blank=True, null=True)
    proj_id = models.IntegerField(blank=True, null=True)
    bdg_header_id = models.TextField()
    iva = models.FloatField()
    cass_prev_proff = models.FloatField()
    inps_dip = models.FloatField()
    st_inpdap = models.FloatField()
    inpdap_118 = models.FloatField()
    st_inpdap_118 = models.FloatField()

    class Meta:
        managed = False
        db_table = 'rtr_charges'


class RtrIncomeTaxes(models.Model):
    id_income = models.AutoField(primary_key=True)
    start_income = models.FloatField()
    end_income = models.FloatField()
    coef_lord_internal = models.FloatField()
    coef_lord_external = models.FloatField()
    irpef = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'rtr_income_taxes'


class RtrSalPcheck(models.Model):
    s_pcheck_id = models.AutoField(primary_key=True)
    pcheck_date = models.BigIntegerField(blank=True, null=True)
    month = models.CharField(max_length=12, blank=True, null=True)
    year = models.CharField(max_length=4, blank=True, null=True)
    gr_sal = models.FloatField()
    iis = models.FloatField()
    sal_13 = models.FloatField()
    iis_13 = models.FloatField()
    vacations = models.FloatField()
    dpcm = models.FloatField()
    g_fund_100 = models.FloatField()
    g_fund_118 = models.FloatField()
    inpdap_100 = models.FloatField()
    inpdap_118 = models.FloatField()
    tax_inc = models.FloatField()
    re_sc_im_n = models.FloatField()
    deduct = models.FloatField()
    re_al_im_n = models.FloatField()
    reg = models.FloatField(blank=True, null=True)
    com = models.FloatField(blank=True, null=True)
    net = models.FloatField()
    st_imp = models.FloatField()
    st_inps = models.FloatField()
    st_irap = models.FloatField()
    st_inpdap_100 = models.FloatField()
    st_inpdap_118 = models.FloatField()
    employee_id = models.IntegerField()
    income = models.BigIntegerField(blank=True, null=True)
    workdays = models.SmallIntegerField(blank=True, null=True)
    re_sc_imp_net = models.FloatField(blank=True, null=True)
    re_sc_imp_lor = models.FloatField(blank=True, null=True)
    re_al_imp_net = models.FloatField(blank=True, null=True)
    re_al_imp_lor = models.FloatField(blank=True, null=True)
    imp_118 = models.FloatField(blank=True, null=True)
    imp_100 = models.FloatField(blank=True, null=True)
    imp_dpcm = models.FloatField(blank=True, null=True)
    det_lav_dip = models.FloatField(blank=True, null=True)
    det_con = models.FloatField(blank=True, null=True)
    det_fig = models.FloatField(blank=True, null=True)
    abs = models.SmallIntegerField(blank=True, null=True)
    vac_day = models.FloatField(blank=True, null=True)
    dif_in = models.FloatField(blank=True, null=True)
    dif_out = models.FloatField(blank=True, null=True)
    eng_data = models.CharField(max_length=255, blank=True, null=True)
    workdays_13 = models.IntegerField(blank=True, null=True)
    month_tfr = models.FloatField()
    ass_fml = models.FloatField()

    class Meta:
        managed = False
        db_table = 'rtr_sal_pcheck'


class SchoolType(models.Model):
    description = models.CharField(primary_key=True, max_length=15)
    school_code = models.CharField(max_length=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'school_type'


class Sedi(models.Model):
    name = models.TextField()
    location = models.TextField()
    notes = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'sedi'


class ServiceState(models.Model):
    service_state_id = models.AutoField(primary_key=True)
    employee_id = models.BigIntegerField()
    institute_id = models.BigIntegerField()
    period_start = models.BigIntegerField(blank=True, null=True)
    period_end = models.BigIntegerField(blank=True, null=True)
    continuative_year = models.SmallIntegerField(blank=True, null=True)
    role_type = models.SmallIntegerField(blank=True, null=True)
    authority_type = models.SmallIntegerField(blank=True, null=True)
    service_type = models.SmallIntegerField(blank=True, null=True)
    activity_type = models.CharField(max_length=10, blank=True, null=True)
    position_kind = models.CharField(max_length=10, blank=True, null=True)
    contest_class = models.CharField(max_length=10, blank=True, null=True)
    qualification = models.CharField(max_length=10, blank=True, null=True)
    teaching_post = models.BooleanField(blank=True, null=True)
    titolarity = models.BooleanField(blank=True, null=True)
    print_service_certificate = models.BooleanField(blank=True, null=True)
    service_hours = models.SmallIntegerField(blank=True, null=True)
    contributive_category = models.SmallIntegerField(blank=True, null=True)
    part_time = models.BooleanField(blank=True, null=True)
    professional_profile = models.CharField(max_length=255, blank=True, null=True)
    service_break = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'service_state'


class ServiceTermination(models.Model):
    termination_service_id = models.AutoField(primary_key=True)
    service_state_id = models.BigIntegerField()
    institute_id = models.BigIntegerField()
    week_hours = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'service_termination'


class ServiceType(models.Model):
    service_type_id = models.AutoField(primary_key=True)
    description = models.TextField(blank=True, null=True)
    code = models.CharField(max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'service_type'


class Session(models.Model):
    session_id = models.BigAutoField(primary_key=True)
    uid = models.ForeignKey('Users', models.DO_NOTHING, db_column='uid', blank=True, null=True)
    session_key = models.TextField(blank=True, null=True)
    session_update = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'session'


class SessionSingularity(models.Model):
    id = models.BigAutoField(primary_key=True)
    session_id = models.TextField()
    uid = models.BigIntegerField()
    last_update = models.TextField()

    class Meta:
        managed = False
        db_table = 'session_singularity'


class Settings(models.Model):
    use_revok_prot = models.BooleanField(blank=True, null=True)
    use_exten_prot = models.BooleanField(blank=True, null=True)
    default_school_name = models.CharField(max_length=35, blank=True, null=True)
    school_adr = models.CharField(max_length=50, blank=True, null=True)
    school_city = models.IntegerField(blank=True, null=True)
    budget_year = models.CharField(primary_key=True, max_length=4)
    print_options = models.TextField(blank=True, null=True)
    inventory_minimal = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'settings'


class ShortTerm(models.Model):
    short_term_id = models.AutoField(primary_key=True)
    prof_profile = models.CharField(max_length=2, blank=True, null=True)
    serv_hours_week = models.CharField(max_length=5, blank=True, null=True)
    week_hours_def = models.CharField(max_length=5, blank=True, null=True)
    substitute = models.IntegerField(blank=True, null=True)
    subst_purpose = models.CharField(max_length=10, blank=True, null=True)
    yearly_gross = models.CharField(max_length=10, blank=True, null=True)
    app_date = models.BigIntegerField(blank=True, null=True)
    contract_date = models.BigIntegerField(blank=True, null=True)
    presentation_date = models.BigIntegerField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    import_pers_subst = models.CharField(max_length=40, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'short_term'


class SocialPosition(models.Model):
    soc_pos_id = models.SmallIntegerField(primary_key=True)
    description = models.CharField(unique=True, max_length=255)

    class Meta:
        managed = False
        db_table = 'social_position'


class States(models.Model):
    state_id = models.AutoField(primary_key=True)
    description = models.CharField(max_length=50)
    initials = models.CharField(max_length=10, blank=True, null=True)
    area = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'states'


class Status(models.Model):
    status_id = models.AutoField(primary_key=True)
    status_description = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'status'


class StoragePersonnelPresences(models.Model):
    storage_personnel_presences_id = models.AutoField(primary_key=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    date_start = models.BigIntegerField()
    date_end = models.BigIntegerField()
    extraordinary_authorized = models.IntegerField()
    permission_remain = models.IntegerField()
    vacation_remain = models.IntegerField()
    to_define = models.BigIntegerField()
    period_vacation = models.BigIntegerField()
    period_permission = models.BigIntegerField()
    ext_start_o = models.IntegerField()
    ext_start = models.IntegerField()
    ext_end_o = models.IntegerField()
    ext_end = models.IntegerField()
    note = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'storage_personnel_presences'


class StoragePersonnelStack(models.Model):
    storage_personnel_presences = models.ForeignKey(StoragePersonnelPresences, models.DO_NOTHING, db_column='storage_personnel_presences', blank=True, null=True)
    absence_stack = models.ForeignKey(AbsenceStack, models.DO_NOTHING, db_column='absence_stack', blank=True, null=True)
    stack_denomination = models.CharField(max_length=255)
    value_start_o = models.FloatField()
    value_start = models.FloatField()
    value_end_o = models.FloatField()
    value_end = models.FloatField()
    unit = models.CharField(max_length=1)
    recover = models.BooleanField()
    reset_type_applied = models.SmallIntegerField()

    class Meta:
        managed = False
        db_table = 'storage_personnel_stack'


class Sub(models.Model):
    city_id = models.IntegerField(blank=True, null=True)
    province = models.CharField(max_length=5, blank=True, null=True)
    region = models.CharField(max_length=5, blank=True, null=True)
    zip = models.CharField(max_length=30, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'sub'


class SubstPurpose(models.Model):
    description = models.CharField(max_length=50)
    code = models.CharField(max_length=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'subst_purpose'


class Supplier(models.Model):
    fiscal_code = models.CharField(primary_key=True, max_length=255)
    vat_number = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255)
    address = models.CharField(max_length=255)
    city = models.ForeignKey(Cities, models.DO_NOTHING, blank=True, null=True)
    city_name = models.CharField(max_length=255)
    city_province = models.CharField(max_length=255)
    city_postal_code = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'supplier'


class TStat(models.Model):
    t_stat_id = models.BigAutoField(primary_key=True)
    status = models.ForeignKey(Status, models.DO_NOTHING, blank=True, null=True)
    mode = models.ForeignKey(Modes, models.DO_NOTHING, blank=True, null=True)
    actual_mode_id = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 't_stat'


class TaxResiduals(models.Model):
    id_residual = models.AutoField(primary_key=True)
    year = models.IntegerField(unique=True)
    tasse = models.FloatField(blank=True, null=True)
    contributi = models.FloatField(blank=True, null=True)
    quote = models.FloatField(blank=True, null=True)
    diversi = models.FloatField(blank=True, null=True)
    debito = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tax_residuals'


class TfrCoef(models.Model):
    coef_id = models.AutoField(primary_key=True)
    coef_year = models.BigIntegerField(blank=True, null=True)
    coef_month = models.TextField(blank=True, null=True)
    coef = models.FloatField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tfr_coef'


class TfrData(models.Model):
    tfr_id = models.AutoField(primary_key=True)
    rtr_id = models.BigIntegerField()
    year = models.IntegerField()
    tfr_data = models.TextField()

    class Meta:
        managed = False
        db_table = 'tfr_data'


class TimetableModels(models.Model):
    timetable_models_id = models.AutoField(primary_key=True)
    description = models.TextField()
    model = models.TextField()

    class Meta:
        managed = False
        db_table = 'timetable_models'


class TipiDiTasseVoices(models.Model):
    link_id = models.AutoField(primary_key=True)
    tax_type_id = models.BigIntegerField(blank=True, null=True)
    aggreg_nr = models.TextField(blank=True, null=True)
    voice_nr = models.TextField(blank=True, null=True)
    subvoice_nr = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tipi_di_tasse_voices'


class TrasparenzaVoice(models.Model):
    title = models.CharField(max_length=255)
    content = models.TextField(blank=True, null=True)
    reference = models.TextField(blank=True, null=True)
    parent_voice = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True)
    published = models.BooleanField()
    last_update = models.BigIntegerField(blank=True, null=True)
    index = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'trasparenza_voice'


class TrasparenzaVoiceDocument(models.Model):
    voice = models.OneToOneField(TrasparenzaVoice, models.DO_NOTHING, primary_key=True)
    document = models.ForeignKey(ArchiveDocumentFile, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'trasparenza_voice_document'
        unique_together = (('voice', 'document'),)


class TypeOfAbsence(models.Model):
    code = models.SmallIntegerField(primary_key=True)
    descr = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'type_of_absence'


class UserLog(models.Model):
    date = models.DateTimeField()
    user_id = models.IntegerField()
    user_name = models.CharField(max_length=50, blank=True, null=True)
    section = models.TextField()
    operation = models.TextField()
    used_table = models.CharField(max_length=50, blank=True, null=True)
    used_id = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'user_log'


class Users(models.Model):
    uid = models.AutoField(primary_key=True)
    user_name = models.CharField(unique=True, max_length=255, blank=True, null=True)
    user_password = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, blank=True, null=True)
    surname = models.CharField(max_length=255, blank=True, null=True)
    version = models.CharField(max_length=255, blank=True, null=True)
    lastcheck = models.BigIntegerField(blank=True, null=True)
    enabled = models.IntegerField(blank=True, null=True)
    user_type = models.ForeignKey(Groups, models.DO_NOTHING, db_column='user_type', blank=True, null=True)
    privelege = models.IntegerField(blank=True, null=True)
    email = models.CharField(max_length=40, blank=True, null=True)
    employee_id = models.IntegerField(blank=True, null=True)
    modify_protocol = models.IntegerField(blank=True, null=True)
    super_user = models.BooleanField()
    expiration = models.BigIntegerField()
    couch_id = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'users'


class UsersSettings(models.Model):
    users_settings_id = models.AutoField(primary_key=True)
    user_id = models.IntegerField(blank=True, null=True)
    navigation = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'users_settings'


class WayOfPayment(models.Model):
    id = models.CharField(primary_key=True, max_length=2)
    descr = models.CharField(max_length=35, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'way_of_payment'


class WhAmmortization(models.Model):
    ammort_id = models.BigAutoField(primary_key=True)
    item_type_name = models.TextField(unique=True)
    ammortization_coeff = models.TextField()

    class Meta:
        managed = False
        db_table = 'wh_ammortization'


class WhDischargeRsn(models.Model):
    descr = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_discharge_rsn'


class WhItem(models.Model):
    item_id = models.AutoField(primary_key=True)
    kind = models.IntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    quantity = models.IntegerField(blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    price = models.FloatField(blank=True, null=True)
    order_id = models.IntegerField(blank=True, null=True)
    charge_date = models.BigIntegerField(blank=True, null=True)
    discharge_date = models.BigIntegerField(blank=True, null=True)
    discharged = models.SmallIntegerField(blank=True, null=True)
    quantity_begin = models.IntegerField(blank=True, null=True)
    inventory_kind = models.SmallIntegerField(blank=True, null=True)
    supplier = models.IntegerField(blank=True, null=True)
    respons = models.IntegerField(blank=True, null=True)
    ammort_type_id = models.BigIntegerField(blank=True, null=True)
    initial_value = models.TextField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)
    old_inv_number = models.BigIntegerField()
    inv_number = models.BigIntegerField()
    sede = models.BigIntegerField()
    discharge_motivation = models.SmallIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_item'


class WhItemKind(models.Model):
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=30, blank=True, null=True)
    inventory = models.BooleanField(blank=True, null=True)
    mu = models.IntegerField(blank=True, null=True)
    class_field = models.CharField(db_column='class', max_length=20, blank=True, null=True)  # Field renamed because it was a Python reserved word.

    class Meta:
        managed = False
        db_table = 'wh_item_kind'


class WhItemVault(models.Model):
    item_id = models.AutoField(primary_key=True)
    kind = models.IntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    quantity = models.IntegerField(blank=True, null=True)
    value = models.FloatField(blank=True, null=True)
    price = models.FloatField(blank=True, null=True)
    order_id = models.IntegerField(blank=True, null=True)
    charge_date = models.BigIntegerField(blank=True, null=True)
    discharge_date = models.BigIntegerField(blank=True, null=True)
    discharged = models.SmallIntegerField(blank=True, null=True)
    quantity_begin = models.IntegerField(blank=True, null=True)
    inventory_kind = models.SmallIntegerField(blank=True, null=True)
    supplier = models.IntegerField(blank=True, null=True)
    respons = models.IntegerField(blank=True, null=True)
    ammort_type_id = models.BigIntegerField(blank=True, null=True)
    initial_value = models.TextField(blank=True, null=True)
    note = models.TextField(blank=True, null=True)
    old_inv_number = models.BigIntegerField()
    inv_number = models.BigIntegerField()
    sede = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'wh_item_vault'


class WhOrder(models.Model):
    order_id = models.AutoField(primary_key=True)
    header_id = models.IntegerField()
    kind = models.IntegerField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    quantity = models.FloatField(blank=True, null=True)
    inventory_kind = models.CharField(max_length=15, blank=True, null=True)
    cost = models.FloatField(blank=True, null=True)
    total_cost = models.FloatField(blank=True, null=True)
    discount = models.IntegerField(blank=True, null=True)
    vat = models.IntegerField(blank=True, null=True)
    sede = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_order'


class WhOrderHeader(models.Model):
    date = models.BigIntegerField(blank=True, null=True)
    year = models.CharField(max_length=4, blank=True, null=True)
    description = models.CharField(max_length=100, blank=True, null=True)
    protocol_id = models.IntegerField(blank=True, null=True)
    approval_number = models.CharField(max_length=15, blank=True, null=True)
    supplier = models.IntegerField(blank=True, null=True)
    offer_number = models.CharField(max_length=15, blank=True, null=True)
    offer_date = models.BigIntegerField(blank=True, null=True)
    int_id = models.IntegerField(blank=True, null=True)
    confirmed = models.SmallIntegerField(blank=True, null=True)
    voice_id = models.IntegerField(blank=True, null=True)
    respons = models.IntegerField(blank=True, null=True)
    bdg_fund = models.IntegerField(blank=True, null=True)
    eng_id = models.IntegerField(blank=True, null=True)
    discount = models.IntegerField(blank=True, null=True)
    shipping = models.FloatField(blank=True, null=True)
    vat_shipping = models.IntegerField(blank=True, null=True)
    other = models.FloatField(blank=True, null=True)
    vat_other = models.IntegerField(blank=True, null=True)
    protocol_num = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_order_header'


class WhResponsible(models.Model):
    name = models.CharField(max_length=25, blank=True, null=True)
    surname = models.CharField(max_length=40, blank=True, null=True)
    location = models.CharField(max_length=200, blank=True, null=True)
    sede = models.BigIntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_responsible'


class WhSmpitemsHistory(models.Model):
    item_id = models.IntegerField()
    discharge_date = models.BigIntegerField()
    quantity = models.IntegerField()
    number = models.IntegerField(blank=True, null=True)
    whom = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_smpitems_history'


class WhSuppliers(models.Model):
    name = models.TextField(blank=True, null=True)
    referent = models.CharField(max_length=50, blank=True, null=True)
    fiscal_code = models.CharField(max_length=30, blank=True, null=True)
    contact1 = models.IntegerField(blank=True, null=True)
    contact2 = models.IntegerField(blank=True, null=True)
    bank_id = models.IntegerField(blank=True, null=True)
    wh_show = models.BooleanField()
    is_fisical = models.BooleanField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    partita_iva = models.TextField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'wh_suppliers'


class WidgetExceptions(models.Model):
    exception_id = models.BigAutoField(primary_key=True)
    template_id = models.BigIntegerField(blank=True, null=True)
    widget_id = models.BigIntegerField(blank=True, null=True)
    t_stat = models.ForeignKey(TStat, models.DO_NOTHING, blank=True, null=True)
    mode = models.ForeignKey(Modes, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'widget_exceptions'


class Widgets(models.Model):
    widget_id = models.BigIntegerField()
    widget_mode = models.TextField()
    template_id = models.BigIntegerField()
    widget_type = models.TextField()
    widget_name = models.TextField()
    widget_data = models.TextField()
    id = models.BigAutoField(primary_key=True)

    class Meta:
        managed = False
        db_table = 'widgets'


class ZipCodes(models.Model):
    zip_code_id = models.AutoField(primary_key=True)
    zip_code = models.CharField(max_length=30)
    city_id = models.IntegerField()
    kind_of_street = models.CharField(max_length=30, blank=True, null=True)
    name_of_street = models.CharField(max_length=50, blank=True, null=True)
    numerical_interval = models.CharField(max_length=10, blank=True, null=True)
    notes = models.CharField(max_length=255, blank=True, null=True)
    default_zip_city = models.BooleanField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'zip_codes'


class Zone(models.Model):
    zone_id = models.AutoField(primary_key=True)
    zone_description = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'zone'


class ZoneTemplates(models.Model):
    id = models.BigAutoField(primary_key=True)
    template = models.TextField()
    zone_id = models.BigIntegerField()
    level = models.IntegerField()
    parent_template = models.BigIntegerField()

    class Meta:
        managed = False
        db_table = 'zone_templates'
