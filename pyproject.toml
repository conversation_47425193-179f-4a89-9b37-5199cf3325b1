[tool.poetry]
name = "mc2"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <m.<PERSON><PERSON><PERSON>@mastertraining.it>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
django = "3.1.14"
psycopg2-binary = "^2.9.10"
setuptools = "^75.6.0"  # Needed for Python 3.12 and later with older versions of Django
djangorestframework = "^3.15.1"
pydantic = "^2.10.3"
stripe = "^11.4.0"
python-dotenv = "^1.0.1"
pyopenssl = "^24.3.0"
cryptography = "^44.0.0"
prometheus-client = "^0.21.1"
gunicorn = "^23.0.0"
requests = "^2.32.4"

[tool.poetry.group.test.dependencies]
coverage = "^7.6.8"
factory-boy = "^3.3.1"

[tool.poetry.group.dev.dependencies]
ipdb = "^0.13.13"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
