#!/bin/sh
set -e

# if [ "$1" = 'uwsgi' ]; then
#     echo 'Migrate'
#     su-exec uwsgi python3 /src/manage.py migrate --noinput
#     echo su-exec uwsgi $*
#     exec su-exec uwsgi $*
# elif [ "$1" = 'nginx' ]; then
#     chown nobody /var/tmp/nginx
#     exec nginx -c /src/docker/nginx.conf -g "daemon off;"
# else
#     exec "$@"
# fi

if [ "$1" = 'gunicorn' ]; then
    echo 'Running migrations...'
    su-exec appuser python3 /src/manage.py migrate --noinput
    echo "Starting Gunicorn: $*"
    exec su-exec appuser "$@"
elif [ "$1" = 'nginx' ]; then
    chown nobody /var/tmp/nginx
    exec nginx -c /src/docker/nginx.conf -g "daemon off;"
else
    exec "$@"
fi